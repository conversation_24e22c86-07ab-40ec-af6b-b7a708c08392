using Core.Exceptions;
using Domain.Features.Message.Queries.GetAppointmentDetailById;
using FluentValidation;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using TestPhccNotifier.TestUtilities;

namespace TestPhccNotifier.Domain.Features.Message.Queries.GetAppointmentDetailById;

public class GetAppointmentDetailByIdHandlerTests
{
    private readonly Mock<ServiceClient> _mockServiceClient;
    private readonly GetAppointmentDetailByIdHandler _handler;

    public GetAppointmentDetailByIdHandlerTests()
    {
        _mockServiceClient = TestHelper.CreateMockServiceClient();
        _handler = new GetAppointmentDetailByIdHandler(_mockServiceClient.Object);
    }

    [Fact]
    public async Task Handle_WithValidId_ShouldReturnAppointmentDetails()
    {
        // Arrange
        var request = new GetAppointmentDetailByIdRequest { Id = "TEST-ID-123" };
        var messageEntity = CreateTestMessageEntity();

        _mockServiceClient
            .Setup(x => x.RetrieveMultiple(It.Is<QueryExpression>(q =>
                q.EntityName == "phcc_message" &&
                q.Criteria.Conditions.Any(c =>
                    c.AttributeName == "phcc_id" &&
                    c.Values.Contains(request.Id)))))
            .Returns(new EntityCollection(new List<Entity> { messageEntity }));

        // Act
        var response = await _handler.Handle(request, CancellationToken.None);

        // Assert
        response.Should().NotBeNull();
        response.ActivityId.Should().Be(messageEntity.Id);
        response.Id.Should().Be("TEST-ID-123");
        response.AppointmentType.Should().Be("Cancer Screening");
        response.MessageId.Should().Be("MSG-12345");
    }

    [Fact]
    public async Task Handle_WithNonExistentId_ShouldThrowBusinessException()
    {
        // Arrange
        var request = new GetAppointmentDetailByIdRequest { Id = "NON-EXISTENT-ID" };

        _mockServiceClient
            .Setup(x => x.RetrieveMultiple(It.IsAny<QueryExpression>()))
            .Returns(new EntityCollection());

        // Act & Assert
        var exception = await _handler.Invoking(h => h.Handle(request, CancellationToken.None))
            .Should().ThrowAsync<BusinessException>();
        
        exception.Which.Message.Should().Contain($"There is no appointment by {request.Id}");
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public async Task Handle_WithEmptyId_ShouldThrowValidationException(string id)
    {
        // Arrange
        var request = new GetAppointmentDetailByIdRequest { Id = id };

        // Act & Assert
        await _handler.Invoking(h => h.Handle(request, CancellationToken.None))
            .Should().ThrowAsync<ValidationException>();
    }

    [Fact]
    public async Task Handle_ShouldQueryWithCorrectCriteria()
    {
        // Arrange
        var request = new GetAppointmentDetailByIdRequest { Id = "TEST-ID-123" };
        var messageEntity = CreateTestMessageEntity();

        QueryExpression? capturedQuery = null;
        _mockServiceClient
            .Setup(x => x.RetrieveMultiple(It.IsAny<QueryExpression>()))
            .Callback<QueryExpression>(q => capturedQuery = q)
            .Returns(new EntityCollection(new List<Entity> { messageEntity }));

        // Act
        await _handler.Handle(request, CancellationToken.None);

        // Assert
        capturedQuery.Should().NotBeNull();
        capturedQuery!.EntityName.Should().Be("phcc_message");
        capturedQuery.Criteria.Conditions.Should().Contain(c => 
            c.AttributeName == "phcc_id" && 
            c.Operator == ConditionOperator.Equal &&
            c.Values.Contains(request.Id));
        capturedQuery.ColumnSet.Columns.Should().Contain("phcc_id");
        capturedQuery.ColumnSet.Columns.Should().Contain("phcc_appointmenttype");
        capturedQuery.ColumnSet.Columns.Should().Contain("phcc_messageid");
    }

    [Fact]
    public async Task Handle_WithMessageHavingNullValues_ShouldHandleGracefully()
    {
        // Arrange
        var request = new GetAppointmentDetailByIdRequest { Id = "TEST-ID-123" };
        var messageEntity = new Entity("phcc_message", Guid.NewGuid());
        messageEntity["phcc_id"] = "TEST-ID-123";
        // Intentionally not setting other fields to test null handling

        _mockServiceClient
            .Setup(x => x.RetrieveMultiple(It.IsAny<QueryExpression>()))
            .Returns(new EntityCollection(new List<Entity> { messageEntity }));

        // Act
        var response = await _handler.Handle(request, CancellationToken.None);

        // Assert
        response.Should().NotBeNull();
        response.ActivityId.Should().Be(messageEntity.Id);
        response.Id.Should().Be("TEST-ID-123");
        response.AppointmentType.Should().BeNull();
        response.MessageId.Should().BeNull();
    }

    [Fact]
    public async Task Handle_ShouldUseAsyncMethod()
    {
        // Arrange
        var request = new GetAppointmentDetailByIdRequest { Id = "TEST-ID-123" };
        var messageEntity = CreateTestMessageEntity();

        _mockServiceClient
            .Setup(x => x.RetrieveMultiple(It.IsAny<QueryExpression>()))
            .Returns(new EntityCollection(new List<Entity> { messageEntity }));

        // Act
        await _handler.Handle(request, CancellationToken.None);

        // Assert
        _mockServiceClient.Verify(x => x.RetrieveMultiple(It.IsAny<QueryExpression>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithCancellationToken_ShouldRespectCancellation()
    {
        // Arrange
        var request = new GetAppointmentDetailByIdRequest { Id = "TEST-ID-123" };
        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();

        _mockServiceClient
            .Setup(x => x.RetrieveMultiple(It.IsAny<QueryExpression>()))
            .Returns(new EntityCollection(new List<Entity> { CreateTestMessageEntity() }));

        // Act & Assert
        await _handler.Invoking(h => h.Handle(request, cancellationTokenSource.Token))
            .Should().ThrowAsync<OperationCanceledException>();
    }

    private Entity CreateTestMessageEntity()
    {
        var entity = new Entity("phcc_message", Guid.NewGuid());
        entity["phcc_id"] = "TEST-ID-123";
        entity["phcc_appointmenttype"] = "Cancer Screening";
        entity["phcc_messageid"] = "MSG-12345";
        return entity;
    }
}
