using AutoFixture;
using Core.Constants;
using Core.Exceptions;
using Domain.Features.Message;
using Domain.Features.Message.Commands.NewAppointment;
using FluentValidation;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using TestPhccNotifier.TestUtilities;

namespace TestPhccNotifier.Domain.Features.Message.Commands.NewAppointment;

public class NewAppointmentHandlerTests
{
    private readonly Mock<ServiceClient> _mockServiceClient;
    private readonly Mock<MessageManager> _mockMessageManager;
    private readonly NewAppointmentHandler _handler;
    private readonly IFixture _fixture;

    public NewAppointmentHandlerTests()
    {
        _mockServiceClient = TestHelper.CreateMockServiceClient();
        _mockMessageManager = new Mock<MessageManager>(_mockServiceClient.Object);
        _handler = new NewAppointmentHandler(_mockServiceClient.Object, _mockMessageManager.Object);
        _fixture = TestHelper.CreateFixture();
    }

    [Fact]
    public async Task Handle_WithValidRequest_ShouldCreateMessageAndReturnResponse()
    {
        // Arrange
        var request = CreateValidNewAppointmentRequest();
        var messageId = Guid.NewGuid();
        var messageEntity = TestHelper.CreateTestMessageEntity();
        messageEntity["phcc_messageid"] = "MSG-12345";

        SetupMocksForSuccessfulCreation(request, messageId, messageEntity);

        // Act
        var response = await _handler.Handle(request, CancellationToken.None);

        // Assert
        response.Should().NotBeNull();
        response.Id.Should().Be(messageId);
        response.MessageId.Should().Be("MSG-12345");
    }

    [Fact]
    public async Task Handle_WithActiveAppointmentExists_ShouldThrowBusinessException()
    {
        // Arrange
        var request = CreateValidNewAppointmentRequest();
        var activeMessages = new List<Entity> { TestHelper.CreateTestMessageEntity() };

        _mockMessageManager
            .Setup(x => x.GetActiveMessages(request.CustomIdentifier))
            .ReturnsAsync(activeMessages);

        // Act & Assert
        var exception = await _handler.Invoking(h => h.Handle(request, CancellationToken.None))
            .Should().ThrowAsync<BusinessException>();
        
        exception.Which.Message.Should().Contain("active upcoming appointment");
    }

    [Fact]
    public async Task Handle_WithExistingAppointmentIdentifier_ShouldThrowBusinessException()
    {
        // Arrange
        var request = CreateValidNewAppointmentRequest();

        _mockMessageManager
            .Setup(x => x.GetActiveMessages(request.CustomIdentifier))
            .ReturnsAsync(new List<Entity>());

        _mockMessageManager
            .Setup(x => x.IsExistAppointmentMessage(request.AppointmentIdentifier))
            .Returns(true);

        // Act & Assert
        var exception = await _handler.Invoking(h => h.Handle(request, CancellationToken.None))
            .Should().ThrowAsync<BusinessException>();
        
        exception.Which.Message.Should().Contain("already a record for this appointment");
    }

    [Fact]
    public async Task Handle_WithReminderNeeded_ShouldCreateBothMessages()
    {
        // Arrange
        var request = CreateValidNewAppointmentRequest();
        var messageId = Guid.NewGuid();
        var messageEntity = TestHelper.CreateTestMessageEntity();

        SetupMocksForSuccessfulCreation(request, messageId, messageEntity);
        
        _mockMessageManager
            .Setup(x => x.IsAppointmentReminder(request.AppointmentDateTime, request.ReminderSendHoursBefore))
            .Returns(true);

        // Act
        var response = await _handler.Handle(request, CancellationToken.None);

        // Assert
        response.Should().NotBeNull();
        
        // Verify both messages were created (NewAppointment and Reminder)
        _mockServiceClient.Verify(x => x.CreateAsync(It.IsAny<Entity>(), It.IsAny<CancellationToken>()), Times.Exactly(2));
    }

    [Fact]
    public async Task Handle_WithNoReminderNeeded_ShouldCreateOnlyOneMessage()
    {
        // Arrange
        var request = CreateValidNewAppointmentRequest();
        var messageId = Guid.NewGuid();
        var messageEntity = TestHelper.CreateTestMessageEntity();

        SetupMocksForSuccessfulCreation(request, messageId, messageEntity);
        
        _mockMessageManager
            .Setup(x => x.IsAppointmentReminder(request.AppointmentDateTime, request.ReminderSendHoursBefore))
            .Returns(false);

        // Act
        var response = await _handler.Handle(request, CancellationToken.None);

        // Assert
        response.Should().NotBeNull();
        
        // Verify only one message was created
        _mockServiceClient.Verify(x => x.CreateAsync(It.IsAny<Entity>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithInvalidRequest_ShouldThrowValidationException()
    {
        // Arrange
        var invalidRequest = new NewAppointmentRequest
        {
            CustomIdentifier = "INVALID", // Should start with HC and be 10 chars
            AppointmentDateTime = DateTime.Now.AddDays(-1) // Should be in future
        };

        // Act & Assert
        await _handler.Invoking(h => h.Handle(invalidRequest, CancellationToken.None))
            .Should().ThrowAsync<ValidationException>();
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    [InlineData("HC123")] // Too short
    [InlineData("HC12345678901")] // Too long
    [InlineData("AB1234567890")] // Doesn't start with HC
    public async Task Handle_WithInvalidCustomIdentifier_ShouldThrowValidationException(string customIdentifier)
    {
        // Arrange
        var request = CreateValidNewAppointmentRequest();
        request.CustomIdentifier = customIdentifier;

        // Act & Assert
        await _handler.Invoking(h => h.Handle(request, CancellationToken.None))
            .Should().ThrowAsync<ValidationException>();
    }

    private NewAppointmentRequest CreateValidNewAppointmentRequest()
    {
        return new NewAppointmentRequest
        {
            AppointmentIdentifier = TestHelper.GenerateValidAppointmentId(),
            AppointmentType = "Cancer Screening",
            AppointmentLocation = "LOC001",
            AppointmentLocationDescription = "Main Clinic",
            AppointmentDateTime = TestHelper.GetTestDateTime(),
            AppointmentArriveMinutesBefore = 30,
            CustomIdentifier = TestHelper.GenerateValidHealthCardNumber(),
            SubjectIdentifier = "SUBJ-123",
            RecipientFirstName = "John",
            RecipientLastName = "Doe",
            RecipientPhone = "+97412345678",
            RecipientNationality = "Qatari",
            ReminderSendHoursBefore = 24,
            ProcedureName = "Mammography",
            ProcedureCode = "MAM001"
        };
    }

    private void SetupMocksForSuccessfulCreation(NewAppointmentRequest request, Guid messageId, Entity messageEntity)
    {
        _mockMessageManager
            .Setup(x => x.GetActiveMessages(request.CustomIdentifier))
            .ReturnsAsync(new List<Entity>());

        _mockMessageManager
            .Setup(x => x.IsExistAppointmentMessage(request.AppointmentIdentifier))
            .Returns(false);

        _mockMessageManager
            .Setup(x => x.GetPatientByHealthCardNumber(request.CustomIdentifier))
            .Returns(TestHelper.CreateTestPatientEntity());

        _mockMessageManager
            .Setup(x => x.GetLocationByCode(request.AppointmentLocation))
            .Returns(TestHelper.CreateTestLocationEntity());

        _mockServiceClient
            .Setup(x => x.CreateAsync(It.IsAny<Entity>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(messageId);

        _mockServiceClient
            .Setup(x => x.RetrieveAsync("phcc_message", messageId, It.IsAny<ColumnSet>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(messageEntity);
    }
}
