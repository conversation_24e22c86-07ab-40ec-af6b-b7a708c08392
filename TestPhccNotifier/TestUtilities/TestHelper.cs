using AutoFixture;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk;
using Core.Configurations;
using Domain.Features.Message;

namespace TestPhccNotifier.TestUtilities;

public static class TestHelper
{
    public static IFixture CreateFixture()
    {
        var fixture = new Fixture();
        
        // Configure AutoFixture to handle circular references
        fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
            .ForEach(b => fixture.Behaviors.Remove(b));
        fixture.Behaviors.Add(new OmitOnRecursionBehavior());
        
        return fixture;
    }

    public static IConfiguration CreateTestConfiguration()
    {
        var configData = new Dictionary<string, string>
        {
            ["Users:0:Id"] = "863183B2-A908-4B60-B705-EEB3078288B2",
            ["Users:0:Name"] = "Test",
            ["Users:0:ClientId"] = "test",
            ["Users:0:SecretKey"] = "test",
            ["Users:0:IsActive"] = "true",
            ["Dynamics:OrganizationURI"] = "https://test.crm.dynamics.com",
            ["Dynamics:ClientId"] = "test-client-id",
            ["Dynamics:SecretKey"] = "test-secret-key"
        };

        return new ConfigurationBuilder()
            .AddInMemoryCollection(configData)
            .Build();
    }

    public static List<UserConfig> CreateTestUsers()
    {
        return new List<UserConfig>
        {
            new UserConfig
            {
                Id = Guid.Parse("863183B2-A908-4B60-B705-EEB3078288B2"),
                Name = "Test",
                ClientId = "test",
                SecretKey = "test",
                IsActive = true
            },
            new UserConfig
            {
                Id = Guid.NewGuid(),
                Name = "Inactive User",
                ClientId = "inactive",
                SecretKey = "inactive",
                IsActive = false
            }
        };
    }

    public static Mock<ILogger<T>> CreateMockLogger<T>()
    {
        return new Mock<ILogger<T>>();
    }

    public static Mock<ServiceClient> CreateMockServiceClient()
    {
        var mock = new Mock<ServiceClient>();
        
        // Setup common methods
        mock.Setup(x => x.IsReady).Returns(true);
        
        return mock;
    }

    public static Entity CreateTestEntity(string entityName, Guid id)
    {
        var entity = new Entity(entityName, id);
        return entity;
    }

    public static Entity CreateTestPatientEntity()
    {
        var patient = new Entity("contact", Guid.NewGuid());
        patient["firstname"] = "John";
        patient["lastname"] = "Doe";
        patient["mobilephone"] = "+97412345678";
        patient["phcc_nationality"] = "Qatari";
        patient["phcc_healthcardnumber"] = "HC12345678";
        return patient;
    }

    public static Entity CreateTestLocationEntity()
    {
        var location = new Entity("msemr_codeableconcept", Guid.NewGuid());
        location["msemr_text"] = "Test Location";
        location["phcc_textarabic"] = "موقع الاختبار";
        location["msemr_code"] = "LOC001";
        location["phcc_iscancerscreeninghc"] = true;
        return location;
    }

    public static Entity CreateTestMessageEntity()
    {
        var message = new Entity("phcc_message", Guid.NewGuid());
        message["phcc_messageid"] = "MSG-" + DateTime.Now.Ticks;
        message["phcc_id"] = "TEST-ID-123";
        message["phcc_appointmenttype"] = "Cancer Screening";
        message["phcc_messagetype"] = new OptionSetValue(1); // NewAppointment
        message["phcc_messagestatus"] = new OptionSetValue(1); // Draft
        return message;
    }

    public static DateTime GetTestDateTime()
    {
        return TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow.AddDays(1), 
            TimeZoneInfo.FindSystemTimeZoneById("Arab Standard Time"));
    }

    public static string GenerateValidHealthCardNumber()
    {
        return "HC" + new Random().Next(10000000, 99999999).ToString();
    }

    public static string GenerateValidAppointmentId()
    {
        return "APT-" + DateTime.Now.Ticks;
    }
}
