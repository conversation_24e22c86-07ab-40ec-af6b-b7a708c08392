# PowerShell script to run tests and generate coverage report

Write-Host "🧪 PHCC Cancer Screening SMS API - Test Runner" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Check if .NET is installed
try {
    $dotnetVersion = dotnet --version
    Write-Host "✅ .NET Version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ .NET is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

# Restore packages
Write-Host "`n📦 Restoring NuGet packages..." -ForegroundColor Yellow
dotnet restore
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Package restore failed" -ForegroundColor Red
    exit 1
}

# Build the project
Write-Host "`n🔨 Building test project..." -ForegroundColor Yellow
dotnet build --no-restore
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed" -ForegroundColor Red
    exit 1
}

# Run tests
Write-Host "`n🧪 Running unit tests..." -ForegroundColor Yellow
dotnet test --no-build --verbosity normal
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Some tests failed" -ForegroundColor Red
    exit 1
}

# Run tests with coverage
Write-Host "`n📊 Running tests with coverage..." -ForegroundColor Yellow
dotnet test --no-build --collect:"XPlat Code Coverage" --verbosity normal

Write-Host "`n✅ All tests completed successfully!" -ForegroundColor Green
Write-Host "`n📋 Test Summary:" -ForegroundColor Cyan
Write-Host "   • Core Project Tests: Exception handling, Results, Configurations, Constants, Dynamics utilities" -ForegroundColor White
Write-Host "   • Domain Project Tests: MessageManager, Command/Query handlers, Validators" -ForegroundColor White
Write-Host "   • WebAPI Project Tests: Controllers, Filters, Middlewares" -ForegroundColor White
Write-Host "   • Integration Tests: Serialization, Data flow validation" -ForegroundColor White

Write-Host "`n🎯 Coverage areas:" -ForegroundColor Cyan
Write-Host "   • Business Logic: ✅ Nationality mapping, Appointment reminders, Validation rules" -ForegroundColor White
Write-Host "   • Authentication: ✅ Valid/invalid credentials, Active/inactive users" -ForegroundColor White
Write-Host "   • Error Handling: ✅ All exception types with proper HTTP status codes" -ForegroundColor White
Write-Host "   • Data Serialization: ✅ JSON serialization/deserialization" -ForegroundColor White

Write-Host "`n🚀 Next steps:" -ForegroundColor Cyan
Write-Host "   • Run 'dotnet test --filter Category=Unit' for unit tests only" -ForegroundColor White
Write-Host "   • Run 'dotnet test --filter FullyQualifiedName~Core' for Core tests only" -ForegroundColor White
Write-Host "   • Run 'dotnet test --filter FullyQualifiedName~Domain' for Domain tests only" -ForegroundColor White
Write-Host "   • Run 'dotnet test --filter FullyQualifiedName~WebAPI' for WebAPI tests only" -ForegroundColor White
