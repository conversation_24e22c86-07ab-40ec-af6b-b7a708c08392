﻿namespace TestPhccNotifier;

public sealed class Test1
{
    [Fact]
    public void TestMethod1()
    {
        // Simple XUnit test to verify the conversion
        var result = 2 + 2;
        Assert.Equal(4, result);
    }

    [Theory]
    [InlineData(1, 1, 2)]
    [InlineData(2, 3, 5)]
    [InlineData(-1, 1, 0)]
    public void TestAddition(int a, int b, int expected)
    {
        var result = a + b;
        Assert.Equal(expected, result);
    }
}