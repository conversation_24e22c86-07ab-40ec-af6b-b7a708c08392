using Core.Dynamics;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using TestPhccNotifier.TestUtilities;

namespace TestPhccNotifier.Core.Dynamics;

public class ServiceExtensionsTests
{
    private readonly Mock<IOrganizationService> _mockOrganizationService;
    private readonly Mock<ServiceClient> _mockServiceClient;

    public ServiceExtensionsTests()
    {
        _mockOrganizationService = new Mock<IOrganizationService>();
        _mockServiceClient = new Mock<ServiceClient>();
    }

    [Fact]
    public void RetrieveFirst_WithResults_ShouldReturnFirstEntity()
    {
        // Arrange
        var entity1 = TestHelper.CreateTestEntity("test", Guid.NewGuid());
        var entity2 = TestHelper.CreateTestEntity("test", Guid.NewGuid());
        var entityCollection = new EntityCollection(new[] { entity1, entity2 });

        var queryExpression = new QueryExpression("test");
        
        _mockOrganizationService
            .Setup(x => x.RetrieveMultiple(It.IsAny<QueryExpression>()))
            .Returns(entityCollection);

        // Act
        var result = _mockOrganizationService.Object.RetrieveFirst(queryExpression);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(entity1.Id);
        
        // Verify query was modified correctly
        queryExpression.NoLock.Should().BeTrue();
        queryExpression.TopCount.Should().Be(1);
        queryExpression.PageInfo.Should().BeNull();
    }

    [Fact]
    public void RetrieveFirst_WithNoResults_ShouldReturnNull()
    {
        // Arrange
        var emptyCollection = new EntityCollection();
        var queryExpression = new QueryExpression("test");
        
        _mockOrganizationService
            .Setup(x => x.RetrieveMultiple(It.IsAny<QueryExpression>()))
            .Returns(emptyCollection);

        // Act
        var result = _mockOrganizationService.Object.RetrieveFirst(queryExpression);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task RetrieveFirstAsync_WithResults_ShouldReturnFirstEntity()
    {
        // Arrange
        var entity1 = TestHelper.CreateTestEntity("test", Guid.NewGuid());
        var entity2 = TestHelper.CreateTestEntity("test", Guid.NewGuid());
        var entityCollection = new EntityCollection(new[] { entity1, entity2 });

        var queryExpression = new QueryExpression("test");
        
        _mockServiceClient
            .Setup(x => x.RetrieveMultipleAsync(It.IsAny<QueryExpression>()))
            .ReturnsAsync(entityCollection);

        // Act
        var result = await _mockServiceClient.Object.RetrieveFirstAsync(queryExpression);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(entity1.Id);
        
        // Verify query was modified correctly
        queryExpression.NoLock.Should().BeTrue();
        queryExpression.TopCount.Should().Be(1);
        queryExpression.PageInfo.Should().BeNull();
    }

    [Fact]
    public async Task RetrieveFirstAsync_WithNoResults_ShouldReturnNull()
    {
        // Arrange
        var emptyCollection = new EntityCollection();
        var queryExpression = new QueryExpression("test");
        
        _mockServiceClient
            .Setup(x => x.RetrieveMultipleAsync(It.IsAny<QueryExpression>()))
            .ReturnsAsync(emptyCollection);

        // Act
        var result = await _mockServiceClient.Object.RetrieveFirstAsync(queryExpression);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void RetrieveAll_WithMultiplePages_ShouldReturnAllEntities()
    {
        // Arrange
        var queryExpression = new QueryExpression("test");
        
        var page1Entities = Enumerable.Range(1, 3)
            .Select(i => TestHelper.CreateTestEntity("test", Guid.NewGuid()))
            .ToArray();
        var page2Entities = Enumerable.Range(1, 2)
            .Select(i => TestHelper.CreateTestEntity("test", Guid.NewGuid()))
            .ToArray();

        var page1Collection = new EntityCollection(page1Entities) { MoreRecords = true };
        var page2Collection = new EntityCollection(page2Entities) { MoreRecords = false };

        _mockOrganizationService
            .SetupSequence(x => x.RetrieveMultiple(It.IsAny<QueryExpression>()))
            .Returns(page1Collection)
            .Returns(page2Collection);

        // Act
        var result = _mockOrganizationService.Object.RetrieveAll(queryExpression);

        // Assert
        result.Entities.Should().HaveCount(5);
        result.Entities.Take(3).Should().BeEquivalentTo(page1Entities);
        result.Entities.Skip(3).Should().BeEquivalentTo(page2Entities);
        
        // Verify query was modified correctly
        queryExpression.NoLock.Should().BeTrue();
        queryExpression.TopCount.Should().BeNull();
    }

    [Fact]
    public void RetrieveAll_WithSinglePage_ShouldReturnAllEntities()
    {
        // Arrange
        var queryExpression = new QueryExpression("test");
        var entities = Enumerable.Range(1, 3)
            .Select(i => TestHelper.CreateTestEntity("test", Guid.NewGuid()))
            .ToArray();

        var entityCollection = new EntityCollection(entities) { MoreRecords = false };

        _mockOrganizationService
            .Setup(x => x.RetrieveMultiple(It.IsAny<QueryExpression>()))
            .Returns(entityCollection);

        // Act
        var result = _mockOrganizationService.Object.RetrieveAll(queryExpression);

        // Assert
        result.Entities.Should().HaveCount(3);
        result.Entities.Should().BeEquivalentTo(entities);
    }

    [Fact]
    public async Task RetrieveAllAsync_WithMultiplePages_ShouldReturnAllEntities()
    {
        // Arrange
        var queryExpression = new QueryExpression("test");
        
        var page1Entities = Enumerable.Range(1, 3)
            .Select(i => TestHelper.CreateTestEntity("test", Guid.NewGuid()))
            .ToArray();
        var page2Entities = Enumerable.Range(1, 2)
            .Select(i => TestHelper.CreateTestEntity("test", Guid.NewGuid()))
            .ToArray();

        var page1Collection = new EntityCollection(page1Entities) { MoreRecords = true };
        var page2Collection = new EntityCollection(page2Entities) { MoreRecords = false };

        _mockServiceClient
            .SetupSequence(x => x.RetrieveMultipleAsync(It.IsAny<QueryExpression>()))
            .ReturnsAsync(page1Collection)
            .ReturnsAsync(page2Collection);

        // Act
        var result = await _mockServiceClient.Object.RetrieveAllAsync(queryExpression);

        // Assert
        result.Entities.Should().HaveCount(5);
        result.Entities.Take(3).Should().BeEquivalentTo(page1Entities);
        result.Entities.Skip(3).Should().BeEquivalentTo(page2Entities);
    }

    [Fact]
    public void RetrieveAll_ShouldSetCorrectPagingInfo()
    {
        // Arrange
        var queryExpression = new QueryExpression("test");
        var entityCollection = new EntityCollection() { MoreRecords = false };

        _mockOrganizationService
            .Setup(x => x.RetrieveMultiple(It.Is<QueryExpression>(q => 
                q.PageInfo != null && 
                q.PageInfo.Count == 5000 && 
                q.PageInfo.PageNumber == 1)))
            .Returns(entityCollection);

        // Act
        _mockOrganizationService.Object.RetrieveAll(queryExpression);

        // Assert
        _mockOrganizationService.Verify(x => x.RetrieveMultiple(It.IsAny<QueryExpression>()), Times.Once);
    }
}
