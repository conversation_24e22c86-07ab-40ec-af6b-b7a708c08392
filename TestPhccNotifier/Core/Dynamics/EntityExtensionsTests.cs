using Core.Dynamics;
using Microsoft.Xrm.Sdk;
using TestPhccNotifier.TestUtilities;

namespace TestPhccNotifier.Core.Dynamics;

public class EntityExtensionsTests
{
    [Fact]
    public void ConvertAliasedValues_WithAliasedValues_ShouldConvertToDirectValues()
    {
        // Arrange
        var entity = new Entity("test");
        var aliasedValue1 = new AliasedValue("entity1", "attribute1", "value1");
        var aliasedValue2 = new AliasedValue("entity2", "attribute2", 123);
        
        entity["aliased_attr1"] = aliasedValue1;
        entity["aliased_attr2"] = aliasedValue2;
        entity["normal_attr"] = "normal_value";

        // Act
        entity.ConvertAliasedValues();

        // Assert
        entity["aliased_attr1"].Should().Be("value1");
        entity["aliased_attr2"].Should().Be(123);
        entity["normal_attr"].Should().Be("normal_value");
    }

    [Fact]
    public void ConvertAliasedValues_WithNoAliasedValues_ShouldNotChangeEntity()
    {
        // Arrange
        var entity = new Entity("test");
        entity["attr1"] = "value1";
        entity["attr2"] = 123;
        entity["attr3"] = true;

        var originalAttributes = entity.Attributes.ToDictionary(x => x.Key, x => x.Value);

        // Act
        entity.ConvertAliasedValues();

        // Assert
        entity.Attributes.Should().BeEquivalentTo(originalAttributes);
    }

    [Fact]
    public void ConvertAliasedValues_WithEmptyEntity_ShouldNotThrow()
    {
        // Arrange
        var entity = new Entity("test");

        // Act & Assert
        entity.Invoking(e => e.ConvertAliasedValues()).Should().NotThrow();
    }

    [Fact]
    public void GetLookup_WithValidEntityReference_ShouldReturnLookup()
    {
        // Arrange
        var entity = new Entity("test");
        var entityRef = new EntityReference("contact", Guid.NewGuid()) { Name = "John Doe" };
        entity["contact_ref"] = entityRef;

        // Act
        var lookup = entity.GetLookup("contact_ref");

        // Assert
        lookup.Should().NotBeNull();
        lookup!.Id.Should().Be(entityRef.Id);
        lookup.Name.Should().Be(entityRef.Name);
    }

    [Fact]
    public void GetLookup_WithNonExistentAttribute_ShouldReturnNull()
    {
        // Arrange
        var entity = new Entity("test");

        // Act
        var lookup = entity.GetLookup("non_existent_attr");

        // Assert
        lookup.Should().BeNull();
    }

    [Fact]
    public void GetLookup_WithNullEntityReference_ShouldReturnNull()
    {
        // Arrange
        var entity = new Entity("test");
        entity["null_ref"] = null;

        // Act
        var lookup = entity.GetLookup("null_ref");

        // Assert
        lookup.Should().BeNull();
    }

    [Fact]
    public void GetLookup_WithNonEntityReferenceValue_ShouldThrow()
    {
        // Arrange
        var entity = new Entity("test");
        entity["string_attr"] = "not an entity reference";

        // Act & Assert
        entity.Invoking(e => e.GetLookup("string_attr"))
            .Should().Throw<InvalidCastException>();
    }

    [Fact]
    public void GetString_WithValidStringAttribute_ShouldReturnString()
    {
        // Arrange
        var entity = new Entity("test");
        entity["string_attr"] = "test value";

        // Act
        var result = entity.GetString("string_attr");

        // Assert
        result.Should().Be("test value");
    }

    [Fact]
    public void GetString_WithNonExistentAttribute_ShouldReturnNull()
    {
        // Arrange
        var entity = new Entity("test");

        // Act
        var result = entity.GetString("non_existent");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void GetString_WithNullValue_ShouldReturnNull()
    {
        // Arrange
        var entity = new Entity("test");
        entity["null_attr"] = null;

        // Act
        var result = entity.GetString("null_attr");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void ConvertAliasedValues_WithMixedAttributeTypes_ShouldOnlyConvertAliasedValues()
    {
        // Arrange
        var entity = new Entity("test");
        var aliasedValue = new AliasedValue("entity1", "attribute1", "aliased_value");
        
        entity["aliased"] = aliasedValue;
        entity["string"] = "string_value";
        entity["number"] = 42;
        entity["boolean"] = true;
        entity["guid"] = Guid.NewGuid();

        // Act
        entity.ConvertAliasedValues();

        // Assert
        entity["aliased"].Should().Be("aliased_value");
        entity["string"].Should().Be("string_value");
        entity["number"].Should().Be(42);
        entity["boolean"].Should().Be(true);
        entity["guid"].Should().BeOfType<Guid>();
    }
}
