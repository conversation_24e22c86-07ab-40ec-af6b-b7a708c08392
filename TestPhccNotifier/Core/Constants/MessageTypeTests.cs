using Core.Constants;

namespace TestPhccNotifier.Core.Constants;

public class MessageTypeTests
{
    [Theory]
    [InlineData(MessageType.NewAppointment, 1)]
    [InlineData(MessageType.CancelAppointment, 2)]
    [InlineData(MessageType.RescheduleAppointment, 3)]
    [InlineData(MessageType.AppointmentReminder, 4)]
    [InlineData(MessageType.NegativeResult, 5)]
    [InlineData(MessageType.NoShow, 6)]
    public void MessageType_ShouldHaveCorrectValues(MessageType messageType, int expectedValue)
    {
        // Act & Assert
        ((int)messageType).Should().Be(expectedValue);
    }

    [Fact]
    public void MessageType_ShouldHaveAllExpectedValues()
    {
        // Arrange
        var expectedValues = new[]
        {
            MessageType.NewAppointment,
            MessageType.CancelAppointment,
            MessageType.RescheduleAppointment,
            MessageType.AppointmentReminder,
            MessageType.NegativeResult,
            MessageType.NoShow
        };

        // Act
        var actualValues = Enum.GetValues<MessageType>();

        // Assert
        actualValues.Should().BeEquivalentTo(expectedValues);
    }

    [Fact]
    public void MessageType_ShouldBeConvertibleToInt()
    {
        // Arrange
        var messageType = MessageType.NewAppointment;

        // Act
        int intValue = (int)messageType;

        // Assert
        intValue.Should().Be(1);
    }

    [Fact]
    public void MessageType_ShouldBeConvertibleFromInt()
    {
        // Arrange
        const int intValue = 1;

        // Act
        var messageType = (MessageType)intValue;

        // Assert
        messageType.Should().Be(MessageType.NewAppointment);
    }

    [Theory]
    [InlineData(1, MessageType.NewAppointment)]
    [InlineData(2, MessageType.CancelAppointment)]
    [InlineData(3, MessageType.RescheduleAppointment)]
    [InlineData(4, MessageType.AppointmentReminder)]
    [InlineData(5, MessageType.NegativeResult)]
    [InlineData(6, MessageType.NoShow)]
    public void MessageType_IntToEnum_ShouldConvertCorrectly(int intValue, MessageType expectedMessageType)
    {
        // Act
        var messageType = (MessageType)intValue;

        // Assert
        messageType.Should().Be(expectedMessageType);
    }

    [Fact]
    public void MessageType_ToString_ShouldReturnEnumName()
    {
        // Arrange
        var messageType = MessageType.NewAppointment;

        // Act
        var stringValue = messageType.ToString();

        // Assert
        stringValue.Should().Be("NewAppointment");
    }

    [Fact]
    public void MessageType_ShouldBeDefined()
    {
        // Act & Assert
        foreach (MessageType messageType in Enum.GetValues<MessageType>())
        {
            Enum.IsDefined(typeof(MessageType), messageType).Should().BeTrue();
        }
    }

    [Theory]
    [InlineData(0)]
    [InlineData(7)]
    [InlineData(-1)]
    [InlineData(100)]
    public void MessageType_InvalidValues_ShouldNotBeDefined(int invalidValue)
    {
        // Act & Assert
        Enum.IsDefined(typeof(MessageType), invalidValue).Should().BeFalse();
    }
}
