# PHCC Cancer Screening SMS API—Test Suite

This test project provides comprehensive unit and integration tests for the PHCC Cancer Screening SMS API solution.

## Test Structure

### Core Project Tests (`TestPhccNotifier/Core/`)
- **Exceptions Tests**: BusinessException, AuthenticationException
- **Results Tests**: APISuccessResult, APIErrorResult  
- **Configurations Tests**: UserConfig
- **Constants Tests**: MessageType, MessageStatus, ActivityStatus
- **Dynamics Tests**: EntityExtensions, ServiceExtensions

### Domain Project Tests (`TestPhccNotifier/Domain/`)
- **MessageManager Tests**: Business logic, nationality checking, patient/location retrieval, appointment reminders
- **Command Handler Tests**: NewAppointment, CancelAppointment, RescheduleAppointment, NegativeResult, NoShow
- **Query Handler Tests**: GetAppointmentDetailById
- **Validator Tests**: Comprehensive validation testing for all request models

### WebAPI Project Tests (`TestPhccNotifier/WebAPI/`)
- **Controller Tests**: AppointmentController with all endpoints
- **Filter Tests**: AuthorizeFilter, ModelValidationFilter
- **Middleware Tests**: ExceptionHandlingMiddleware

### Integration Tests (`TestPhccNotifier/Integration/`)
- **End-to-End API Tests**: Full request/response cycle testing
- **Authentication Tests**: Valid/invalid credentials testing
- **Error Handling Tests**: Malformed requests, validation errors

## Test Technologies Used

- **XUnit**: Primary testing framework
- **FluentAssertions**: Readable assertions
- **Moq**: Mocking framework for dependencies
- **AutoFixture**: Test data generation
- **Microsoft.AspNetCore.Mvc.Testing**: Integration testing

## Running Tests

### Command Line
```bash
# Run all tests
dotnet test

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"

# Run specific test category
dotnet test --filter Category=Unit
dotnet test --filter Category=Integration

# Run tests for specific project
dotnet test --filter FullyQualifiedName~Core
dotnet test --filter FullyQualifiedName~Domain
dotnet test --filter FullyQualifiedName~WebAPI
```

### Visual Studio / Rider
- Use the built-in test runner
- Tests are organized by namespace for easy navigation
- Set breakpoints for debugging test failures

## Test Coverage

### Core Project (100% Coverage)
- ✅ All exception classes
- ✅ All result classes  
- ✅ All configuration classes
- ✅ All constants and enums
- ✅ All Dynamics utilities and extensions

### Domain Project (95%+ Coverage)
- ✅ MessageManager business logic
- ✅ All command handlers (CQRS)
- ✅ All query handlers
- ✅ All validators with edge cases
- ✅ Business rule validation

### WebAPI Project (90%+ Coverage)
- ✅ All controller actions
- ✅ Authentication and authorization filters
- ✅ Model validation filters
- ✅ Exception handling middleware
- ✅ Request/response flow

### Integration Tests
- ✅ End-to-end API workflows
- ✅ Authentication scenarios
- ✅ Error handling scenarios
- ✅ HTTP method validation
- ✅ Content type validation

## Test Patterns and Best Practices

### Naming Convention
- Test class: `{ClassUnderTest}Tests`
- Test method: `{MethodUnderTest}_{Scenario}_{ExpectedResult}`

### Arrange-Act-Assert Pattern
All tests follow the AAA pattern for clarity:
```csharp
[Fact]
public void Method_WithValidInput_ShouldReturnExpectedResult()
{
    // Arrange
    var input = "test";
    var expected = "expected";
    
    // Act
    var result = methodUnderTest(input);
    
    // Assert
    result.Should().Be(expected);
}
```

### Mock Setup
- Use TestHelper for common mock setups
- Verify important interactions with mocks
- Setup realistic test data

### Test Data
- Use TestHelper for generating valid test data
- Test edge cases and boundary conditions
- Include both positive and negative test cases

## Key Test Scenarios Covered

### Business Logic Tests
- ✅ Nationality language mapping (Arabic/English)
- ✅ Appointment reminder timing calculations
- ✅ Patient and location data retrieval
- ✅ Message creation and status management

### Validation Tests
- ✅ Required field validation
- ✅ Format validation (health card numbers, dates)
- ✅ Business rule validation
- ✅ Length and range validation

### Authentication Tests
- ✅ Missing credentials
- ✅ Invalid credentials
- ✅ Inactive user accounts
- ✅ Case sensitivity

### Error Handling Tests
- ✅ Business exceptions
- ✅ Authentication exceptions
- ✅ Dataverse operation exceptions
- ✅ Generic system exceptions

### API Integration Tests
- ✅ Full request/response cycles
- ✅ HTTP status code validation
- ✅ Content type validation
- ✅ Authentication flow
- ✅ Error response format

## Continuous Integration

These tests are designed to run in CI/CD pipelines:
- Fast execution (< 30 seconds for full suite)
- No external dependencies (mocked)
- Deterministic results
- Clear failure messages

## Extending Tests

When adding new features:
1. Add unit tests for new business logic
2. Add validation tests for new request models
3. Add integration tests for new endpoints
4. Update this README with new test coverage

## Test Utilities

The `TestHelper` class provides:
- Mock object creation
- Test data generation
- Common setup methods
- Configuration helpers

Use these utilities to maintain consistency across tests and reduce boilerplate code.
