using Core.Results;
using Domain.Features.Message.Commands.NewAppointment;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Net;
using System.Text;
using System.Text.Json;
using TestPhccNotifier.TestUtilities;

namespace TestPhccNotifier.Integration;

public class AppointmentIntegrationTests
{
    private readonly JsonSerializerOptions _jsonOptions;

    public AppointmentIntegrationTests()
    {
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    [Fact]
    public void NewAppointmentRequest_Serialization_ShouldWorkCorrectly()
    {
        // Arrange
        var request = CreateValidNewAppointmentRequest();

        // Act
        var json = JsonSerializer.Serialize(request, _jsonOptions);
        var deserializedRequest = JsonSerializer.Deserialize<NewAppointmentRequest>(json, _jsonOptions);

        // Assert
        deserializedRequest.Should().NotBeNull();
        deserializedRequest!.AppointmentIdentifier.Should().Be(request.AppointmentIdentifier);
        deserializedRequest.CustomIdentifier.Should().Be(request.CustomIdentifier);
        deserializedRequest.RecipientFirstName.Should().Be(request.RecipientFirstName);
    }

    [Fact]
    public void APIErrorResult_Serialization_ShouldWorkCorrectly()
    {
        // Arrange
        var errorResult = new APIErrorResult(APIErrorType.ValidationError, "Test error",
            new Dictionary<string, IEnumerable<string>>
            {
                { "Field1", new[] { "Error 1", "Error 2" } }
            });

        // Act
        var json = JsonSerializer.Serialize(errorResult, _jsonOptions);
        var deserializedResult = JsonSerializer.Deserialize<APIErrorResult>(json, _jsonOptions);

        // Assert
        deserializedResult.Should().NotBeNull();
        deserializedResult!.ErrorType.Should().Be((int)APIErrorType.ValidationError);
        deserializedResult.Message.Should().Be("Test error");
        deserializedResult.ValidationErrors.Should().ContainKey("Field1");
    }

    [Fact]
    public void APISuccessResult_Serialization_ShouldWorkCorrectly()
    {
        // Arrange
        var successResult = new APISuccessResult<NewAppointmentRequest>(
            CreateValidNewAppointmentRequest(),
            "Request processed successfully");

        // Act
        var json = JsonSerializer.Serialize(successResult, _jsonOptions);
        var deserializedResult = JsonSerializer.Deserialize<APISuccessResult<NewAppointmentRequest>>(json, _jsonOptions);

        // Assert
        deserializedResult.Should().NotBeNull();
        deserializedResult!.Message.Should().Be("Request processed successfully");
        deserializedResult.Data.Should().NotBeNull();
        deserializedResult.Data!.AppointmentType.Should().Be("Cancer Screening");
    }

    [Fact]
    public void RequestValidation_WithInvalidData_ShouldProduceValidationErrors()
    {
        // Arrange
        var invalidRequest = new NewAppointmentRequest
        {
            CustomIdentifier = "INVALID", // Should start with HC and be 10 chars
            AppointmentDateTime = DateTime.Now.AddDays(-1) // Should be in future
        };

        // Act
        var json = JsonSerializer.Serialize(invalidRequest, _jsonOptions);
        var deserializedRequest = JsonSerializer.Deserialize<NewAppointmentRequest>(json, _jsonOptions);

        // Assert
        deserializedRequest.Should().NotBeNull();
        deserializedRequest!.CustomIdentifier.Should().Be("INVALID");
        deserializedRequest.AppointmentDateTime.Should().BeBefore(DateTime.Now);

        // This demonstrates that the data can be serialized/deserialized
        // but would fail validation when processed by the validator
    }

    [Fact]
    public void AllRequestTypes_ShouldSerializeCorrectly()
    {
        // Arrange
        var requests = new object[]
        {
            CreateValidNewAppointmentRequest(),
            CreateValidCancelAppointmentRequest(),
            CreateValidNegativeResultRequest(),
            CreateValidNoShowRequest()
        };

        // Act & Assert
        foreach (var request in requests)
        {
            var json = JsonSerializer.Serialize(request, _jsonOptions);
            json.Should().NotBeNullOrEmpty();
            json.Should().Contain("customIdentifier");

            // Verify it can be deserialized back
            var requestType = request.GetType();
            var deserializedRequest = JsonSerializer.Deserialize(json, requestType, _jsonOptions);
            deserializedRequest.Should().NotBeNull();
        }
    }

    [Fact]
    public void JsonSerialization_WithComplexObjects_ShouldHandleNullValues()
    {
        // Arrange
        var request = CreateValidNewAppointmentRequest();
        request.ProcedureName = null;
        request.ProcedureCode = null;

        // Act
        var json = JsonSerializer.Serialize(request, _jsonOptions);
        var deserializedRequest = JsonSerializer.Deserialize<NewAppointmentRequest>(json, _jsonOptions);

        // Assert
        deserializedRequest.Should().NotBeNull();
        deserializedRequest!.ProcedureName.Should().BeNull();
        deserializedRequest.ProcedureCode.Should().BeNull();
        deserializedRequest.AppointmentIdentifier.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public void JsonSerialization_WithDateTimes_ShouldPreserveValues()
    {
        // Arrange
        var request = CreateValidNewAppointmentRequest();
        var originalDateTime = request.AppointmentDateTime;

        // Act
        var json = JsonSerializer.Serialize(request, _jsonOptions);
        var deserializedRequest = JsonSerializer.Deserialize<NewAppointmentRequest>(json, _jsonOptions);

        // Assert
        deserializedRequest.Should().NotBeNull();
        deserializedRequest!.AppointmentDateTime.Should().BeCloseTo(originalDateTime, TimeSpan.FromSeconds(1));
    }

    [Theory]
    [InlineData("")]
    [InlineData("{ invalid json }")]
    [InlineData("null")]
    [InlineData("[]")]
    public void JsonDeserialization_WithInvalidJson_ShouldHandleGracefully(string invalidJson)
    {
        // Act & Assert
        var action = () => JsonSerializer.Deserialize<NewAppointmentRequest>(invalidJson, _jsonOptions);

        // Should either return null or throw JsonException, but not crash the application
        action.Should().NotThrow<NullReferenceException>();
        action.Should().NotThrow<InvalidOperationException>();
    }

    private NewAppointmentRequest CreateValidNewAppointmentRequest()
    {
        return new NewAppointmentRequest
        {
            AppointmentIdentifier = TestHelper.GenerateValidAppointmentId(),
            AppointmentType = "Cancer Screening",
            AppointmentLocation = "LOC001",
            AppointmentLocationDescription = "Main Clinic",
            AppointmentDateTime = TestHelper.GetTestDateTime(),
            AppointmentArriveMinutesBefore = 30,
            CustomIdentifier = TestHelper.GenerateValidHealthCardNumber(),
            SubjectIdentifier = "SUBJ-123",
            RecipientFirstName = "John",
            RecipientLastName = "Doe",
            RecipientMobileNumber = "+97412345678",
            RecipientNationality = "Qatari",
            ReminderSendHoursBefore = 24,
            ProcedureName = "Mammography",
            ProcedureCode = "MAM001"
        };
    }

    private object CreateValidCancelAppointmentRequest()
    {
        return new
        {
            AppointmentIdentifier = TestHelper.GenerateValidAppointmentId(),
            AppointmentType = "Cancer Screening",
            AppointmentLocation = "LOC001",
            AppointmentLocationDescription = "Main Clinic",
            AppointmentDateTime = TestHelper.GetTestDateTime(),
            AppointmentArriveMinutesBefore = 30,
            CustomIdentifier = TestHelper.GenerateValidHealthCardNumber(),
            SubjectIdentifier = "SUBJ-123",
            RecipientFirstName = "John",
            RecipientLastName = "Doe",
            RecipientMobileNumber = "+97412345678",
            RecipientNationality = "Qatari"
        };
    }

    private object CreateValidNegativeResultRequest()
    {
        return new
        {
            CustomIdentifier = TestHelper.GenerateValidHealthCardNumber(),
            SubjectIdentifier = "SUBJ-123",
            RecipientFirstName = "John",
            RecipientLastName = "Doe",
            RecipientMobileNumber = "+97412345678",
            RecipientNationality = "Qatari"
        };
    }

    private object CreateValidNoShowRequest()
    {
        return new
        {
            AppointmentDateTime = TestHelper.GetTestDateTime(),
            AppointmentArriveMinutesBefore = 30,
            CustomIdentifier = TestHelper.GenerateValidHealthCardNumber(),
            SubjectIdentifier = "SUBJ-123",
            RecipientFirstName = "John",
            RecipientLastName = "Doe",
            RecipientMobileNumber = "+97412345678",
            RecipientNationality = "Qatari"
        };
    }


}
