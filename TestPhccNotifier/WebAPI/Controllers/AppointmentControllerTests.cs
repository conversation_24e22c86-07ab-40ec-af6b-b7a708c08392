using Core.Results;
using Domain.Features.Message.Commands.CancelAppointment;
using Domain.Features.Message.Commands.NegativeResult;
using Domain.Features.Message.Commands.NewAppointment;
using Domain.Features.Message.Commands.NoShow;
using Domain.Features.Message.Commands.RescheduleAppointment;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using TestPhccNotifier.TestUtilities;
using WebAPI.Controllers;

namespace TestPhccNotifier.WebAPI.Controllers;

public class AppointmentControllerTests
{
    private readonly Mock<ILogger<AppointmentController>> _mockLogger;
    private readonly Mock<IMediator> _mockMediator;
    private readonly AppointmentController _controller;

    public AppointmentControllerTests()
    {
        _mockLogger = TestHelper.CreateMockLogger<AppointmentController>();
        _mockMediator = new Mock<IMediator>();
        _controller = new AppointmentController(_mockLogger.Object, _mockMediator.Object);
    }

    [Fact]
    public async Task NewAppointment_WithValidRequest_ShouldReturnCreatedResult()
    {
        // Arrange
        var request = CreateValidNewAppointmentRequest();
        var response = new NewAppointmentResponse
        {
            Id = Guid.NewGuid(),
            MessageId = "MSG-12345"
        };

        _mockMediator
            .Setup(x => x.Send(request, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.NewAppointment(request);

        // Assert
        result.Should().BeOfType<CreatedResult>();
        var createdResult = result as CreatedResult;
        createdResult!.Value.Should().BeOfType<APISuccessResult<NewAppointmentResponse>>();
        
        var apiResult = createdResult.Value as APISuccessResult<NewAppointmentResponse>;
        apiResult!.Data.Should().Be(response);
    }

    [Fact]
    public async Task NewAppointment_ShouldCallMediatorWithCorrectRequest()
    {
        // Arrange
        var request = CreateValidNewAppointmentRequest();
        var response = new NewAppointmentResponse { Id = Guid.NewGuid(), MessageId = "MSG-12345" };

        _mockMediator
            .Setup(x => x.Send(request, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        await _controller.NewAppointment(request);

        // Assert
        _mockMediator.Verify(x => x.Send(request, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task RescheduleAppointment_WithValidRequest_ShouldReturnCreatedResult()
    {
        // Arrange
        var request = CreateValidRescheduleAppointmentRequest();
        var response = new RescheduleAppointmentResponse
        {
            Id = Guid.NewGuid(),
            MessageId = "MSG-RESCHEDULE-12345"
        };

        _mockMediator
            .Setup(x => x.Send(request, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.RescheduleAppointment(request);

        // Assert
        result.Should().BeOfType<CreatedResult>();
        var createdResult = result as CreatedResult;
        createdResult!.Value.Should().BeOfType<APISuccessResult<RescheduleAppointmentResponse>>();
        
        var apiResult = createdResult.Value as APISuccessResult<RescheduleAppointmentResponse>;
        apiResult!.Data.Should().Be(response);
    }

    [Fact]
    public async Task CancelAppointment_WithValidRequest_ShouldReturnCreatedResult()
    {
        // Arrange
        var request = CreateValidCancelAppointmentRequest();
        var response = new CancelAppointmentResponse
        {
            Id = Guid.NewGuid(),
            MessageId = "MSG-CANCEL-12345"
        };

        _mockMediator
            .Setup(x => x.Send(request, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.CancelAppointment(request);

        // Assert
        result.Should().BeOfType<CreatedResult>();
        var createdResult = result as CreatedResult;
        createdResult!.Value.Should().BeOfType<APISuccessResult<CancelAppointmentResponse>>();
        
        var apiResult = createdResult.Value as APISuccessResult<CancelAppointmentResponse>;
        apiResult!.Data.Should().Be(response);
    }

    [Fact]
    public async Task NegativeResult_WithValidRequest_ShouldReturnCreatedResult()
    {
        // Arrange
        var request = CreateValidNegativeResultRequest();
        var response = new NegativeResultResponse
        {
            Id = Guid.NewGuid(),
            MessageId = "MSG-NEGATIVE-12345"
        };

        _mockMediator
            .Setup(x => x.Send(request, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.NegativeResult(request);

        // Assert
        result.Should().BeOfType<CreatedResult>();
        var createdResult = result as CreatedResult;
        createdResult!.Value.Should().BeOfType<APISuccessResult<NegativeResultResponse>>();
        
        var apiResult = createdResult.Value as APISuccessResult<NegativeResultResponse>;
        apiResult!.Data.Should().Be(response);
    }

    [Fact]
    public async Task NoShow_WithValidRequest_ShouldReturnCreatedResult()
    {
        // Arrange
        var request = CreateValidNoShowRequest();
        var response = new NoShowResponse
        {
            Id = Guid.NewGuid(),
            MessageId = "MSG-NOSHOW-12345"
        };

        _mockMediator
            .Setup(x => x.Send(request, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.NoShow(request);

        // Assert
        result.Should().BeOfType<CreatedResult>();
        var createdResult = result as CreatedResult;
        createdResult!.Value.Should().BeOfType<APISuccessResult<NoShowResponse>>();
        
        var apiResult = createdResult.Value as APISuccessResult<NoShowResponse>;
        apiResult!.Data.Should().Be(response);
    }

    [Fact]
    public async Task AllEndpoints_ShouldReturnCreatedStatusCode()
    {
        // Arrange
        var newAppointmentRequest = CreateValidNewAppointmentRequest();
        var rescheduleRequest = CreateValidRescheduleAppointmentRequest();
        var cancelRequest = CreateValidCancelAppointmentRequest();
        var negativeResultRequest = CreateValidNegativeResultRequest();
        var noShowRequest = CreateValidNoShowRequest();

        SetupMediatorForAllRequests();

        // Act & Assert
        var newResult = await _controller.NewAppointment(newAppointmentRequest);
        var rescheduleResult = await _controller.RescheduleAppointment(rescheduleRequest);
        var cancelResult = await _controller.CancelAppointment(cancelRequest);
        var negativeResult = await _controller.NegativeResult(negativeResultRequest);
        var noShowResult = await _controller.NoShow(noShowRequest);

        // Assert all return Created (201)
        newResult.Should().BeOfType<CreatedResult>();
        rescheduleResult.Should().BeOfType<CreatedResult>();
        cancelResult.Should().BeOfType<CreatedResult>();
        negativeResult.Should().BeOfType<CreatedResult>();
        noShowResult.Should().BeOfType<CreatedResult>();
    }

    private NewAppointmentRequest CreateValidNewAppointmentRequest()
    {
        return new NewAppointmentRequest
        {
            AppointmentIdentifier = TestHelper.GenerateValidAppointmentId(),
            AppointmentType = "Cancer Screening",
            AppointmentLocation = "LOC001",
            AppointmentLocationDescription = "Main Clinic",
            AppointmentDateTime = TestHelper.GetTestDateTime(),
            AppointmentArriveMinutesBefore = 30,
            CustomIdentifier = TestHelper.GenerateValidHealthCardNumber(),
            SubjectIdentifier = "SUBJ-123",
            RecipientFirstName = "John",
            RecipientLastName = "Doe",
            RecipientMobileNumber = "+97412345678",
            RecipientNationality = "Qatari",
            ReminderSendHoursBefore = 24
        };
    }

    private RescheduleAppointmentRequest CreateValidRescheduleAppointmentRequest()
    {
        return new RescheduleAppointmentRequest
        {
            AppointmentIdentifier = TestHelper.GenerateValidAppointmentId(),
            AppointmentType = "Cancer Screening",
            AppointmentLocation = "LOC001",
            AppointmentLocationDescription = "Main Clinic",
            AppointmentDateTime = TestHelper.GetTestDateTime(),
            AppointmentArriveMinutesBefore = 30,
            CustomIdentifier = TestHelper.GenerateValidHealthCardNumber(),
            SubjectIdentifier = "SUBJ-123",
            RecipientFirstName = "John",
            RecipientLastName = "Doe",
            RecipientMobileNumber = "+97412345678",
            RecipientNationality = "Qatari",
            ReminderSendHoursBefore = 24
        };
    }

    private CancelAppointmentRequest CreateValidCancelAppointmentRequest()
    {
        return new CancelAppointmentRequest
        {
            AppointmentIdentifier = TestHelper.GenerateValidAppointmentId(),
            AppointmentType = "Cancer Screening",
            AppointmentLocation = "LOC001",
            AppointmentLocationDescription = "Main Clinic",
            AppointmentDateTime = TestHelper.GetTestDateTime(),
            AppointmentArriveMinutesBefore = 30,
            CustomIdentifier = TestHelper.GenerateValidHealthCardNumber(),
            SubjectIdentifier = "SUBJ-123",
            RecipientFirstName = "John",
            RecipientLastName = "Doe",
            RecipientMobileNumber = "+97412345678",
            RecipientNationality = "Qatari"
        };
    }

    private NegativeResultRequest CreateValidNegativeResultRequest()
    {
        return new NegativeResultRequest
        {
            CustomIdentifier = TestHelper.GenerateValidHealthCardNumber(),
            SubjectIdentifier = "SUBJ-123",
            RecipientFirstName = "John",
            RecipientLastName = "Doe",
            RecipientMobileNumber = "+97412345678",
            RecipientNationality = "Qatari"
        };
    }

    private NoShowRequest CreateValidNoShowRequest()
    {
        return new NoShowRequest
        {
            AppointmentDateTime = TestHelper.GetTestDateTime(),
            AppointmentArriveMinutesBefore = 30,
            CustomIdentifier = TestHelper.GenerateValidHealthCardNumber(),
            SubjectIdentifier = "SUBJ-123",
            RecipientFirstName = "John",
            RecipientLastName = "Doe",
            RecipientMobileNumber = "+97412345678",
            RecipientNationality = "Qatari"
        };
    }

    private void SetupMediatorForAllRequests()
    {
        _mockMediator.Setup(x => x.Send(It.IsAny<NewAppointmentRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new NewAppointmentResponse { Id = Guid.NewGuid(), MessageId = "MSG-NEW" });
        
        _mockMediator.Setup(x => x.Send(It.IsAny<RescheduleAppointmentRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new RescheduleAppointmentResponse { Id = Guid.NewGuid(), MessageId = "MSG-RESCHEDULE" });
        
        _mockMediator.Setup(x => x.Send(It.IsAny<CancelAppointmentRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new CancelAppointmentResponse { Id = Guid.NewGuid(), MessageId = "MSG-CANCEL" });
        
        _mockMediator.Setup(x => x.Send(It.IsAny<NegativeResultRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new NegativeResultResponse { Id = Guid.NewGuid(), MessageId = "MSG-NEGATIVE" });
        
        _mockMediator.Setup(x => x.Send(It.IsAny<NoShowRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new NoShowResponse { Id = Guid.NewGuid(), MessageId = "MSG-NOSHOW" });
    }
}
