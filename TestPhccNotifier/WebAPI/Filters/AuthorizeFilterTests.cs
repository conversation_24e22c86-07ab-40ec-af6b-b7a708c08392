using Core.Configurations;
using Core.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Primitives;
using TestPhccNotifier.TestUtilities;
using WebAPI.Filters;

namespace TestPhccNotifier.WebAPI.Filters;

public class AuthorizeFilterTests
{
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly AuthorizeFilter _filter;
    private readonly AuthorizationFilterContext _context;

    public AuthorizeFilterTests()
    {
        _mockConfiguration = new Mock<IConfiguration>();
        _filter = new AuthorizeFilter(_mockConfiguration.Object);
        
        var httpContext = new DefaultHttpContext();
        var actionContext = new ActionContext(httpContext, new Microsoft.AspNetCore.Routing.RouteData(), new Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor());
        _context = new AuthorizationFilterContext(actionContext, new List<IFilterMetadata>());
    }

    [Fact]
    public void OnAuthorization_WithValidCredentials_ShouldNotThrow()
    {
        // Arrange
        SetupValidHeaders();
        SetupValidUsers();

        // Act & Assert
        _filter.Invoking(f => f.OnAuthorization(_context)).Should().NotThrow();
    }

    [Fact]
    public void OnAuthorization_WithMissingClientId_ShouldThrowAuthenticationException()
    {
        // Arrange
        _context.HttpContext.Request.Headers["X-Secret-Key"] = "test";
        SetupValidUsers();

        // Act & Assert
        _filter.Invoking(f => f.OnAuthorization(_context))
            .Should().Throw<AuthenticationException>()
            .WithMessage("X-Client-Id parameter is missign.");
    }

    [Fact]
    public void OnAuthorization_WithEmptyClientId_ShouldThrowAuthenticationException()
    {
        // Arrange
        _context.HttpContext.Request.Headers["X-Client-Id"] = "";
        _context.HttpContext.Request.Headers["X-Secret-Key"] = "test";
        SetupValidUsers();

        // Act & Assert
        _filter.Invoking(f => f.OnAuthorization(_context))
            .Should().Throw<AuthenticationException>()
            .WithMessage("X-Client-Id parameter is missign.");
    }

    [Fact]
    public void OnAuthorization_WithMissingSecretKey_ShouldThrowAuthenticationException()
    {
        // Arrange
        _context.HttpContext.Request.Headers["X-Client-Id"] = "test";
        SetupValidUsers();

        // Act & Assert
        _filter.Invoking(f => f.OnAuthorization(_context))
            .Should().Throw<AuthenticationException>()
            .WithMessage("X-Secret-Key parameter is missign.");
    }

    [Fact]
    public void OnAuthorization_WithEmptySecretKey_ShouldThrowAuthenticationException()
    {
        // Arrange
        _context.HttpContext.Request.Headers["X-Client-Id"] = "test";
        _context.HttpContext.Request.Headers["X-Secret-Key"] = "";
        SetupValidUsers();

        // Act & Assert
        _filter.Invoking(f => f.OnAuthorization(_context))
            .Should().Throw<AuthenticationException>()
            .WithMessage("X-Secret-Key parameter is missign.");
    }

    [Fact]
    public void OnAuthorization_WithInvalidCredentials_ShouldThrowAuthenticationException()
    {
        // Arrange
        _context.HttpContext.Request.Headers["X-Client-Id"] = "invalid";
        _context.HttpContext.Request.Headers["X-Secret-Key"] = "invalid";
        SetupValidUsers();

        // Act & Assert
        _filter.Invoking(f => f.OnAuthorization(_context))
            .Should().Throw<AuthenticationException>()
            .WithMessage("ClientId or SecretKey is invalid.");
    }

    [Fact]
    public void OnAuthorization_WithInactiveUser_ShouldThrowAuthenticationException()
    {
        // Arrange
        _context.HttpContext.Request.Headers["X-Client-Id"] = "inactive";
        _context.HttpContext.Request.Headers["X-Secret-Key"] = "inactive";
        SetupValidUsers();

        // Act & Assert
        _filter.Invoking(f => f.OnAuthorization(_context))
            .Should().Throw<AuthenticationException>()
            .WithMessage("This account has been disabled.");
    }

    [Fact]
    public void OnAuthorization_WithValidActiveUser_ShouldPass()
    {
        // Arrange
        _context.HttpContext.Request.Headers["X-Client-Id"] = "test";
        _context.HttpContext.Request.Headers["X-Secret-Key"] = "test";
        SetupValidUsers();

        // Act & Assert
        _filter.Invoking(f => f.OnAuthorization(_context)).Should().NotThrow();
    }

    [Fact]
    public void OnAuthorization_WithCaseSensitiveCredentials_ShouldBeExact()
    {
        // Arrange
        _context.HttpContext.Request.Headers["X-Client-Id"] = "TEST"; // Different case
        _context.HttpContext.Request.Headers["X-Secret-Key"] = "test";
        SetupValidUsers();

        // Act & Assert
        _filter.Invoking(f => f.OnAuthorization(_context))
            .Should().Throw<AuthenticationException>()
            .WithMessage("ClientId or SecretKey is invalid.");
    }

    [Fact]
    public void OnAuthorization_WithMultipleUsers_ShouldFindCorrectUser()
    {
        // Arrange
        _context.HttpContext.Request.Headers["X-Client-Id"] = "user2";
        _context.HttpContext.Request.Headers["X-Secret-Key"] = "secret2";
        
        var users = new List<UserConfig>
        {
            new UserConfig { ClientId = "user1", SecretKey = "secret1", IsActive = true },
            new UserConfig { ClientId = "user2", SecretKey = "secret2", IsActive = true },
            new UserConfig { ClientId = "user3", SecretKey = "secret3", IsActive = true }
        };

        var mockSection = new Mock<IConfigurationSection>();
        mockSection.Setup(x => x.Get<IEnumerable<UserConfig>>()).Returns(users);
        _mockConfiguration.Setup(x => x.GetSection("Users")).Returns(mockSection.Object);

        // Act & Assert
        _filter.Invoking(f => f.OnAuthorization(_context)).Should().NotThrow();
    }

    [Fact]
    public void OnAuthorization_WithNoUsersConfigured_ShouldThrowAuthenticationException()
    {
        // Arrange
        SetupValidHeaders();
        
        var mockSection = new Mock<IConfigurationSection>();
        mockSection.Setup(x => x.Get<IEnumerable<UserConfig>>()).Returns(new List<UserConfig>());
        _mockConfiguration.Setup(x => x.GetSection("Users")).Returns(mockSection.Object);

        // Act & Assert
        _filter.Invoking(f => f.OnAuthorization(_context))
            .Should().Throw<AuthenticationException>()
            .WithMessage("ClientId or SecretKey is invalid.");
    }

    [Fact]
    public void OnAuthorization_WithNullUsersConfiguration_ShouldThrowAuthenticationException()
    {
        // Arrange
        SetupValidHeaders();
        
        var mockSection = new Mock<IConfigurationSection>();
        mockSection.Setup(x => x.Get<IEnumerable<UserConfig>>()).Returns((IEnumerable<UserConfig>?)null);
        _mockConfiguration.Setup(x => x.GetSection("Users")).Returns(mockSection.Object);

        // Act & Assert
        _filter.Invoking(f => f.OnAuthorization(_context))
            .Should().Throw<NullReferenceException>();
    }

    private void SetupValidHeaders()
    {
        _context.HttpContext.Request.Headers["X-Client-Id"] = "test";
        _context.HttpContext.Request.Headers["X-Secret-Key"] = "test";
    }

    private void SetupValidUsers()
    {
        var users = TestHelper.CreateTestUsers();
        var mockSection = new Mock<IConfigurationSection>();
        mockSection.Setup(x => x.Get<IEnumerable<UserConfig>>()).Returns(users);
        _mockConfiguration.Setup(x => x.GetSection("Users")).Returns(mockSection.Object);
    }
}
