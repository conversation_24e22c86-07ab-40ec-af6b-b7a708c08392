using Core.Results;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Routing;
using WebAPI.Filters;

namespace TestPhccNotifier.WebAPI.Filters;

public class ModelValidationFilterTests
{
    private readonly ModelValidationFilter _filter;

    public ModelValidationFilterTests()
    {
        _filter = new ModelValidationFilter();
    }

    [Fact]
    public async Task OnActionExecutionAsync_WithValidModel_ShouldCallNext()
    {
        // Arrange
        var context = CreateActionExecutingContext();
        var nextCalled = false;
        
        Task<ActionExecutedContext> Next()
        {
            nextCalled = true;
            return Task.FromResult(new ActionExecutedContext(context, new List<IFilterMetadata>(), null!));
        }

        // Act
        await _filter.OnActionExecutionAsync(context, Next);

        // Assert
        nextCalled.Should().BeTrue();
        context.Result.Should().BeNull();
    }

    [Fact]
    public async Task OnActionExecutionAsync_WithInvalidModel_ShouldSetBadRequestResult()
    {
        // Arrange
        var context = CreateActionExecutingContext();
        context.ModelState.AddModelError("TestProperty", "Test error message");
        
        var nextCalled = false;
        Task<ActionExecutedContext> Next()
        {
            nextCalled = true;
            return Task.FromResult(new ActionExecutedContext(context, new List<IFilterMetadata>(), null!));
        }

        // Act
        await _filter.OnActionExecutionAsync(context, Next);

        // Assert
        nextCalled.Should().BeFalse();
        context.Result.Should().BeOfType<BadRequestObjectResult>();
        
        var badRequestResult = context.Result as BadRequestObjectResult;
        badRequestResult!.Value.Should().BeOfType<APIErrorResult>();
        
        var errorResult = badRequestResult.Value as APIErrorResult;
        errorResult!.ErrorType.Should().Be((int)APIErrorType.ValidationError);
        errorResult.Message.Should().Be("One or more property are invalid.");
        errorResult.ValidationErrors.Should().ContainKey("TestProperty");
        errorResult.ValidationErrors!["TestProperty"].Should().Contain("Test error message");
    }

    [Fact]
    public async Task OnActionExecutionAsync_WithMultipleValidationErrors_ShouldIncludeAllErrors()
    {
        // Arrange
        var context = CreateActionExecutingContext();
        context.ModelState.AddModelError("Property1", "Error 1");
        context.ModelState.AddModelError("Property1", "Error 2");
        context.ModelState.AddModelError("Property2", "Error 3");
        
        Task<ActionExecutedContext> Next()
        {
            return Task.FromResult(new ActionExecutedContext(context, new List<IFilterMetadata>(), null!));
        }

        // Act
        await _filter.OnActionExecutionAsync(context, Next);

        // Assert
        context.Result.Should().BeOfType<BadRequestObjectResult>();
        
        var badRequestResult = context.Result as BadRequestObjectResult;
        var errorResult = badRequestResult!.Value as APIErrorResult;
        
        errorResult!.ValidationErrors.Should().HaveCount(2);
        errorResult.ValidationErrors!["Property1"].Should().HaveCount(2);
        errorResult.ValidationErrors["Property1"].Should().Contain("Error 1");
        errorResult.ValidationErrors["Property1"].Should().Contain("Error 2");
        errorResult.ValidationErrors["Property2"].Should().Contain("Error 3");
    }

    [Fact]
    public async Task OnActionExecutionAsync_WithEmptyModelStateErrors_ShouldCallNext()
    {
        // Arrange
        var context = CreateActionExecutingContext();
        // Add a model state entry but with no errors
        context.ModelState.SetModelValue("TestProperty", new ValueProviderResult("test"));
        
        var nextCalled = false;
        Task<ActionExecutedContext> Next()
        {
            nextCalled = true;
            return Task.FromResult(new ActionExecutedContext(context, new List<IFilterMetadata>(), null!));
        }

        // Act
        await _filter.OnActionExecutionAsync(context, Next);

        // Assert
        nextCalled.Should().BeTrue();
        context.Result.Should().BeNull();
    }

    [Fact]
    public async Task OnActionExecutionAsync_WithNullModelStateValue_ShouldIgnoreNullEntries()
    {
        // Arrange
        var context = CreateActionExecutingContext();
        context.ModelState.AddModelError("ValidProperty", "Valid error");
        
        // Manually add another error to test multiple properties
        context.ModelState.AddModelError("NullProperty", "Null property error");
        
        Task<ActionExecutedContext> Next()
        {
            return Task.FromResult(new ActionExecutedContext(context, new List<IFilterMetadata>(), null!));
        }

        // Act
        await _filter.OnActionExecutionAsync(context, Next);

        // Assert
        context.Result.Should().BeOfType<BadRequestObjectResult>();
        
        var badRequestResult = context.Result as BadRequestObjectResult;
        var errorResult = badRequestResult!.Value as APIErrorResult;
        
        errorResult!.ValidationErrors.Should().ContainKey("ValidProperty");
        errorResult.ValidationErrors.Should().NotContainKey("NullProperty");
    }

    [Fact]
    public async Task OnActionExecutionAsync_ShouldCreateCorrectAPIErrorResult()
    {
        // Arrange
        var context = CreateActionExecutingContext();
        context.ModelState.AddModelError("TestProperty", "Test error");
        
        Task<ActionExecutedContext> Next()
        {
            return Task.FromResult(new ActionExecutedContext(context, new List<IFilterMetadata>(), null!));
        }

        // Act
        await _filter.OnActionExecutionAsync(context, Next);

        // Assert
        var badRequestResult = context.Result as BadRequestObjectResult;
        var errorResult = badRequestResult!.Value as APIErrorResult;
        
        errorResult!.ErrorType.Should().Be((int)APIErrorType.ValidationError);
        errorResult.ErrorTypeDescription.Should().Be("ValidationError");
        errorResult.Message.Should().Be("One or more property are invalid.");
        errorResult.ValidationErrors.Should().NotBeNull();
        errorResult.StackTrace.Should().BeNull();
    }

    [Fact]
    public async Task OnActionExecutionAsync_WithComplexValidationErrors_ShouldHandleCorrectly()
    {
        // Arrange
        var context = CreateActionExecutingContext();
        context.ModelState.AddModelError("User.Name", "Name is required");
        context.ModelState.AddModelError("User.Email", "Email is invalid");
        context.ModelState.AddModelError("User.Email", "Email is required");
        context.ModelState.AddModelError("Address.Street", "Street is required");
        
        Task<ActionExecutedContext> Next()
        {
            return Task.FromResult(new ActionExecutedContext(context, new List<IFilterMetadata>(), null!));
        }

        // Act
        await _filter.OnActionExecutionAsync(context, Next);

        // Assert
        var badRequestResult = context.Result as BadRequestObjectResult;
        var errorResult = badRequestResult!.Value as APIErrorResult;
        
        errorResult!.ValidationErrors.Should().HaveCount(3);
        errorResult.ValidationErrors!["User.Name"].Should().HaveCount(1);
        errorResult.ValidationErrors["User.Email"].Should().HaveCount(2);
        errorResult.ValidationErrors["Address.Street"].Should().HaveCount(1);
    }

    private ActionExecutingContext CreateActionExecutingContext()
    {
        var httpContext = new DefaultHttpContext();
        var routeData = new RouteData();
        var actionDescriptor = new ActionDescriptor();
        var actionContext = new ActionContext(httpContext, routeData, actionDescriptor);
        
        return new ActionExecutingContext(
            actionContext,
            new List<IFilterMetadata>(),
            new Dictionary<string, object?>(),
            controller: null);
    }
}
