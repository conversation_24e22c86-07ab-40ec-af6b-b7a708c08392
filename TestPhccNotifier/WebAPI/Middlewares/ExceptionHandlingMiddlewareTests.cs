using Core.Exceptions;
using Core.Results;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.PowerPlatform.Dataverse.Client.Utils;
using System.Net;
using System.Text.Json;
using TestPhccNotifier.TestUtilities;
using WebAPI.Middlewares;

namespace TestPhccNotifier.WebAPI.Middlewares;

public class ExceptionHandlingMiddlewareTests
{
    private readonly Mock<ILogger<ExceptionHandlingMiddleware>> _mockLogger;
    private readonly Mock<RequestDelegate> _mockNext;
    private readonly Mock<IWebHostEnvironment> _mockEnvironment;
    private readonly ExceptionHandlingMiddleware _middleware;
    private readonly DefaultHttpContext _httpContext;

    public ExceptionHandlingMiddlewareTests()
    {
        _mockLogger = TestHelper.CreateMockLogger<ExceptionHandlingMiddleware>();
        _mockNext = new Mock<RequestDelegate>();
        _mockEnvironment = new Mock<IWebHostEnvironment>();
        _mockEnvironment.Setup(x => x.EnvironmentName).Returns("Development");
        _middleware = new ExceptionHandlingMiddleware(_mockNext.Object, _mockEnvironment.Object, _mockLogger.Object);
        _httpContext = new DefaultHttpContext();
        _httpContext.Response.Body = new MemoryStream();
    }

    [Fact]
    public async Task InvokeAsync_WithNoException_ShouldCallNext()
    {
        // Arrange
        _mockNext.Setup(x => x(_httpContext)).Returns(Task.CompletedTask);

        // Act
        await _middleware.InvokeAsync(_httpContext);

        // Assert
        _mockNext.Verify(x => x(_httpContext), Times.Once);
    }

    [Fact]
    public async Task InvokeAsync_WithBusinessException_ShouldReturnBadRequestWithErrorResult()
    {
        // Arrange
        var exception = new BusinessException("Test business error");
        _mockNext.Setup(x => x(_httpContext)).ThrowsAsync(exception);

        // Act
        await _middleware.InvokeAsync(_httpContext);

        // Assert
        _httpContext.Response.StatusCode.Should().Be((int)HttpStatusCode.BadRequest);
        _httpContext.Response.ContentType.Should().Be("application/json");

        var responseBody = await GetResponseBody();
        var errorResult = JsonSerializer.Deserialize<APIErrorResult>(responseBody, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        errorResult.Should().NotBeNull();
        errorResult!.ErrorType.Should().Be((int)APIErrorType.BusinessError);
        errorResult.Message.Should().Be("Test business error");
    }

    [Fact]
    public async Task InvokeAsync_WithAuthenticationException_ShouldReturnUnauthorizedWithErrorResult()
    {
        // Arrange
        var exception = new AuthenticationException("Authentication failed");
        _mockNext.Setup(x => x(_httpContext)).ThrowsAsync(exception);

        // Act
        await _middleware.InvokeAsync(_httpContext);

        // Assert
        _httpContext.Response.StatusCode.Should().Be((int)HttpStatusCode.Unauthorized);
        _httpContext.Response.ContentType.Should().Be("application/json");

        var responseBody = await GetResponseBody();
        var errorResult = JsonSerializer.Deserialize<APIErrorResult>(responseBody, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        errorResult.Should().NotBeNull();
        errorResult!.ErrorType.Should().Be((int)APIErrorType.Authentication);
        errorResult.Message.Should().Be("Authentication failed");
    }

    [Fact]
    public async Task InvokeAsync_WithDataverseOperationException_ShouldReturnBadRequestWithErrorResult()
    {
        // Arrange
        var exception = new DataverseOperationException("Dataverse operation failed");
        _mockNext.Setup(x => x(_httpContext)).ThrowsAsync(exception);

        // Act
        await _middleware.InvokeAsync(_httpContext);

        // Assert
        _httpContext.Response.StatusCode.Should().Be((int)HttpStatusCode.BadRequest);
        _httpContext.Response.ContentType.Should().Be("application/json");

        var responseBody = await GetResponseBody();
        var errorResult = JsonSerializer.Deserialize<APIErrorResult>(responseBody, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        errorResult.Should().NotBeNull();
        errorResult!.ErrorType.Should().Be((int)APIErrorType.BusinessError);
        errorResult.Message.Should().Be("Dataverse operation failed");
    }

    [Fact]
    public async Task InvokeAsync_WithGenericException_ShouldReturnInternalServerErrorWithErrorResult()
    {
        // Arrange
        var exception = new InvalidOperationException("Generic error");
        _mockNext.Setup(x => x(_httpContext)).ThrowsAsync(exception);

        // Act
        await _middleware.InvokeAsync(_httpContext);

        // Assert
        _httpContext.Response.StatusCode.Should().Be((int)HttpStatusCode.InternalServerError);
        _httpContext.Response.ContentType.Should().Be("application/json");

        var responseBody = await GetResponseBody();
        var errorResult = JsonSerializer.Deserialize<APIErrorResult>(responseBody, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        errorResult.Should().NotBeNull();
        errorResult!.ErrorType.Should().Be((int)APIErrorType.SystemError);
        errorResult.Message.Should().Be("Generic error");
    }

    [Fact]
    public async Task InvokeAsync_WithException_ShouldLogError()
    {
        // Arrange
        var exception = new BusinessException("Test error");
        _mockNext.Setup(x => x(_httpContext)).ThrowsAsync(exception);

        // Act
        await _middleware.InvokeAsync(_httpContext);

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Test error")),
                exception,
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task InvokeAsync_WithNullException_ShouldHandleGracefully()
    {
        // Arrange
        _mockNext.Setup(x => x(_httpContext)).ThrowsAsync(new ArgumentNullException());

        // Act
        await _middleware.InvokeAsync(_httpContext);

        // Assert
        _httpContext.Response.StatusCode.Should().Be((int)HttpStatusCode.InternalServerError);
    }

    [Theory]
    [InlineData(typeof(BusinessException), HttpStatusCode.BadRequest, APIErrorType.BusinessError)]
    [InlineData(typeof(AuthenticationException), HttpStatusCode.Unauthorized, APIErrorType.Authentication)]
    [InlineData(typeof(DataverseOperationException), HttpStatusCode.BadRequest, APIErrorType.BusinessError)]
    [InlineData(typeof(InvalidOperationException), HttpStatusCode.InternalServerError, APIErrorType.GeneralError)]
    [InlineData(typeof(ArgumentException), HttpStatusCode.InternalServerError, APIErrorType.GeneralError)]
    public async Task InvokeAsync_WithDifferentExceptionTypes_ShouldReturnCorrectStatusAndErrorType(
        Type exceptionType, HttpStatusCode expectedStatusCode, APIErrorType expectedErrorType)
    {
        // Arrange
        var exception = (Exception)Activator.CreateInstance(exceptionType, "Test message")!;
        _mockNext.Setup(x => x(_httpContext)).ThrowsAsync(exception);

        // Act
        await _middleware.InvokeAsync(_httpContext);

        // Assert
        _httpContext.Response.StatusCode.Should().Be((int)expectedStatusCode);

        var responseBody = await GetResponseBody();
        var errorResult = JsonSerializer.Deserialize<APIErrorResult>(responseBody, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        errorResult!.ErrorType.Should().Be((int)expectedErrorType);
    }

    [Fact]
    public async Task InvokeAsync_ShouldSetCorrectContentType()
    {
        // Arrange
        var exception = new BusinessException("Test error");
        _mockNext.Setup(x => x(_httpContext)).ThrowsAsync(exception);

        // Act
        await _middleware.InvokeAsync(_httpContext);

        // Assert
        _httpContext.Response.ContentType.Should().Be("application/json");
    }

    [Fact]
    public async Task InvokeAsync_WithExceptionContainingStackTrace_ShouldIncludeStackTrace()
    {
        // Arrange
        var exception = new InvalidOperationException("Test error with stack trace");
        _mockNext.Setup(x => x(_httpContext)).ThrowsAsync(exception);

        // Act
        await _middleware.InvokeAsync(_httpContext);

        // Assert
        var responseBody = await GetResponseBody();
        var errorResult = JsonSerializer.Deserialize<APIErrorResult>(responseBody, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        errorResult!.StackTrace.Should().NotBeNullOrEmpty();
    }

    private async Task<string> GetResponseBody()
    {
        _httpContext.Response.Body.Seek(0, SeekOrigin.Begin);
        using var reader = new StreamReader(_httpContext.Response.Body);
        return await reader.ReadToEndAsync();
    }
}
