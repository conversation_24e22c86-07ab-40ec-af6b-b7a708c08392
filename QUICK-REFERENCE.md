# PHCC Cancer Screening SMS API - Quick Reference

## 🚀 Quick Start

### Prerequisites
- .NET 6.0 SDK
- Dynamics 365 environment access
- Visual Studio 2022 or JetBrains Rider

### Setup
```bash
git clone https://github.com/public-health-care-center-CORP/CancerScreeningSMS.git
cd CancerScreeningSMS
dotnet restore
dotnet build
cd WebAPI
dotnet run
```

### API Base URL
- Local: `https://localhost:5001`
- Swagger: `https://localhost:5001/swagger`

## 🔐 Authentication

All endpoints require headers:
```http
X-Client-Id: test
X-Secret-Key: test
```

## 📋 API Endpoints

### 1. New Appointment
**POST** `/api/appointment/new`

**Minimal Request:**
```json
{
  "appointmentIdentifier": "12345678",
  "appointmentType": "Breast Screening",
  "appointmentLocation": "RAK",
  "appointmentLocationDescription": "Rawdat Al Khail",
  "appointmentDateTime": "2025-04-30T10:00:00",
  "appointmentArriveMinutesBefore": 30,
  "customIdentifier": "HC00000001",
  "subjectIdentifier": "HC00000001",
  "recipientFirstName": "<PERSON>",
  "recipientLastName": "Doe",
  "recipientPhone": "12345678",
  "recipientEmail": "<EMAIL>",
  "sendDateTime": "2025-04-28T12:48:50",
  "reminderSendHoursBefore": 24,
  "reminderType": "Screening",
  "recipientNationality": "American"
}
```

**With Procedure Details (US3655):**
```json
{
  "appointmentIdentifier": "12345678",
  "appointmentType": "Breast Screening",
  "procedureName": "Screening Mammogram",
  "procedureCode": "77067",
  "appointmentLocation": "RAK",
  "appointmentLocationDescription": "Rawdat Al Khail",
  "appointmentDateTime": "2025-04-30T10:00:00",
  "appointmentArriveMinutesBefore": 30,
  "customIdentifier": "HC00000001",
  "subjectIdentifier": "HC00000001",
  "recipientFirstName": "John",
  "recipientLastName": "Doe",
  "recipientPhone": "12345678",
  "recipientEmail": "<EMAIL>",
  "sendDateTime": "2025-04-28T12:48:50",
  "reminderSendHoursBefore": 24,
  "reminderType": "Screening",
  "recipientNationality": "American"
}
```

### 2. Reschedule Appointment
**POST** `/api/appointment/reschedule`
(Same request structure as new appointment)

### 3. Cancel Appointment
**POST** `/api/appointment/cancel`
```json
{
  "customIdentifier": "HC00000001",
  "appointmentIdentifier": "12345678",
  "appointmentType": "Breast Screening",
  "appointmentLocation": "RAK",
  "appointmentLocationDescription": "Rawdat Al Khail",
  "appointmentDateTime": "2025-04-30T10:00:00",
  "subjectIdentifier": "HC00000001",
  "recipientFirstName": "John",
  "recipientLastName": "Doe",
  "recipientPhone": "12345678",
  "recipientEmail": "<EMAIL>",
  "sendDateTime": "2025-04-28T12:48:50",
  "recipientNationality": "American"
}
```

### 4. Negative Result
**POST** `/api/appointment/negative-result`

### 5. No Show
**POST** `/api/appointment/no-show`

## ✅ Validation Rules

### Required Fields
- `appointmentIdentifier`: Not empty
- `appointmentType`: Not empty
- `appointmentLocation`: Not empty
- `appointmentLocationDescription`: Not empty
- `appointmentDateTime`: Future date
- `customIdentifier`: Exactly 10 chars, starts with "HC"
- `recipientFirstName`: Not empty
- `recipientLastName`: Not empty
- `recipientPhone`: Not empty
- `sendDateTime`: Before appointment date
- `reminderSendHoursBefore`: Not empty
- `reminderType`: Not empty
- `recipientNationality`: Not empty

### Optional Fields (US3655)
- `procedureName`: Max 200 characters
- `procedureCode`: Max 50 characters

## 🌐 Language Support

**Arabic Nationalities:**
Bahraini, Egyptian, Iraqi, Jordanian, Kuwaiti, Lebanese, Mauritania, Moroccan, Omani, Palestinian, Qatar Document, Qatari, Romanian, Saudi, Sudanese, Syrian, Tunisian, Yemeni

**Default:** English for all others

## 🔧 Configuration

### appsettings.json
```json
{
  "Dynamics": {
    "OrganizationURI": "https://yourorg.crm4.dynamics.com/",
    "TenantId": "[tenant-id]",
    "ClientId": "[client-id]",
    "SecretKey": "[secret-key]"
  },
  "Users": [
    {
      "Id": "guid",
      "Name": "Test User",
      "ClientId": "test",
      "SecretKey": "test",
      "IsActive": true
    }
  ]
}
```

## 🚨 Common Errors

### 400 Bad Request
```json
{
  "isSuccess": false,
  "errors": [
    {
      "field": "CustomIdentifier",
      "message": "The CustomIdentifier length must be 10 characters."
    }
  ]
}
```

### 401 Unauthorized
```json
{
  "isSuccess": false,
  "message": "X-Client-Id parameter is missing."
}
```

### 409 Conflict
```json
{
  "isSuccess": false,
  "message": "There is already a record for this appointment identifier."
}
```

## 🧪 Testing

### PowerShell Test
```powershell
$headers = @{
    "X-Client-Id" = "test"
    "X-Secret-Key" = "test"
    "Content-Type" = "application/json"
}

$body = @{
    appointmentIdentifier = "TEST001"
    appointmentType = "Breast Screening"
    procedureName = "Screening Mammogram"
    procedureCode = "77067"
    appointmentLocation = "RAK"
    appointmentLocationDescription = "Rawdat Al Khail"
    appointmentDateTime = "2025-04-30T10:00:00"
    appointmentArriveMinutesBefore = 30
    customIdentifier = "HC00000001"
    subjectIdentifier = "HC00000001"
    recipientFirstName = "John"
    recipientLastName = "Doe"
    recipientPhone = "12345678"
    recipientEmail = "<EMAIL>"
    sendDateTime = "2025-04-28T12:48:50"
    reminderSendHoursBefore = 24
    reminderType = "Screening"
    recipientNationality = "American"
} | ConvertTo-Json

Invoke-RestMethod -Uri "https://localhost:5001/api/appointment/new" -Method POST -Headers $headers -Body $body
```

### cURL Test
```bash
curl -X POST "https://localhost:5001/api/appointment/new" \
  -H "X-Client-Id: test" \
  -H "X-Secret-Key: test" \
  -H "Content-Type: application/json" \
  -d '{
    "appointmentIdentifier": "TEST001",
    "appointmentType": "Breast Screening",
    "procedureName": "Screening Mammogram",
    "procedureCode": "77067",
    "appointmentLocation": "RAK",
    "appointmentLocationDescription": "Rawdat Al Khail",
    "appointmentDateTime": "2025-04-30T10:00:00",
    "appointmentArriveMinutesBefore": 30,
    "customIdentifier": "HC00000001",
    "subjectIdentifier": "HC00000001",
    "recipientFirstName": "John",
    "recipientLastName": "Doe",
    "recipientPhone": "12345678",
    "recipientEmail": "<EMAIL>",
    "sendDateTime": "2025-04-28T12:48:50",
    "reminderSendHoursBefore": 24,
    "reminderType": "Screening",
    "recipientNationality": "American"
  }'
```

## 📊 Monitoring

### Health Check
```bash
curl https://localhost:5001/health
```

### Application Insights
- Request telemetry
- Exception tracking
- Performance monitoring
- Custom events

## 🔗 Related Documents

- [Complete Documentation](DOCUMENTATION.md)
- [US3655 Implementation Notes](US3655-IMPLEMENTATION-NOTES.md)
- [Original README](README.md)

## 📞 Support

- **Development Team**: <EMAIL>
- **Infrastructure**: <EMAIL>
- **Business Team**: <EMAIL>
