﻿using Core.Results;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using WebAPI.Filters;
using Domain.Features.Message.Commands.NewAppointment;
using Domain.Features.Message.Commands.RescheduleAppointment;
using Domain.Features.Message.Commands.CancelAppointment;
using Domain.Features.Message.Commands.NegativeResult;
using Domain.Features.Message.Commands.NoShow;

namespace WebAPI.Controllers
{
    [ApiController]
    [Route("api/appointment")]
    public class AppointmentController : ControllerBase
    {
        private readonly ILogger<AppointmentController> _logger;
        private readonly IMediator _mediator;


        public AppointmentController(ILogger<AppointmentController> logger, IMediator mediator)
        {
            _logger = logger;
            _mediator = mediator;
        }

        [ServiceFilter(typeof(AuthorizeFilter))]
        [HttpPost("new")]
        public async Task<IActionResult> NewAppointment([FromBody] NewAppointmentRequest request)
        {
            var response = await _mediator.Send(request);
            return Created("", new APISuccessResult<NewAppointmentResponse>(response));
        }

        [ServiceFilter(typeof(AuthorizeFilter))]
        [HttpPost("reschedule")]
        public async Task<IActionResult> RescheduleAppointment([FromBody] RescheduleAppointmentRequest request) 
        {
            var response = await _mediator.Send(request);
            return Created("", new APISuccessResult<RescheduleAppointmentResponse>(response));
        }

        [ServiceFilter(typeof(AuthorizeFilter))]
        [HttpPost("cancel")]
        public async Task<IActionResult> CancelAppointment([FromBody] CancelAppointmentRequest request)
        {
            var response = await _mediator.Send(request);
            return Created("", new APISuccessResult<CancelAppointmentResponse>(response));
        }

        [ServiceFilter(typeof(AuthorizeFilter))]
        [HttpPost("negative-result")]
        public async Task<IActionResult> NegativeResult([FromBody] NegativeResultRequest request)
        {
            var response = await _mediator.Send(request);
            return Created("", new APISuccessResult<NegativeResultResponse>(response));
        }

        [ServiceFilter(typeof(AuthorizeFilter))]
        [HttpPost("no-show")]
        public async Task<IActionResult> NoShow([FromBody] NoShowRequest request)
        {
            var response = await _mediator.Send(request);
            return Created("", new APISuccessResult<NoShowResponse>(response));
        }
    }
}
