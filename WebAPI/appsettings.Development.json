{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Dynamics": "", "Swagger": {"UseSwagger": true, "UseSwaggerUI": true}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}]}, "Users": [{"Id": "863183B2-A908-4B60-B705-EEB3078288B2", "Name": "Test", "ClientId": "test", "SecretKey": "test", "IsActive": true}]}