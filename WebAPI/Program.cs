
using Core.Configurations;
using Destructurama;
using Domain;
using Microsoft.ApplicationInsights.Extensibility;
using Serilog;
using WebAPI.Filters;
using WebAPI.Middlewares;
using WebAPI.Setup;

var builder = WebApplication.CreateBuilder(args);


builder.Host.UseSerilog( (context, services, loggerConfiguration) =>{

loggerConfiguration
.WriteTo.
ApplicationInsights(services.GetRequiredService<TelemetryConfiguration>(),TelemetryConverter.Events);  // capturing Events - exceptions 

});




builder.Services.AddApplicationInsightsTelemetry();  // adding AppInsight Telemetry Services
builder.Services.AddScoped<AuthorizeFilter>();

builder.Services
    .RunControllerSetup()
    .RunSwaggerSetup()
    .AddDomainDependencies();


var app = builder.Build();

// if (app.Environment.IsDevelopment()) { }

if (app.Configuration.GetSection("Swagger").Get<SwaggerConfig>().UseSwagger)
    app.UseSwagger();

if (app.Configuration.GetSection("Swagger").Get<SwaggerConfig>().UseSwaggerUI)
    app.UseSwaggerUI();

app.UseMiddleware<SecurityHeadersMiddleware>();
app.UseMiddleware<SerilogLogContextMiddleware>();
app.UseMiddleware<RequestLoggingMiddleware>();
app.UseMiddleware<ExceptionHandlingMiddleware>();

app.UseHttpsRedirection();

app.UseAuthorization();

app.MapControllers();

Log.Logger = new LoggerConfiguration()
    .Destructure.UsingAttributes()
    .Enrich.FromLogContext()
    .Enrich.WithProperty("Application", "PHCC.SMS")
    .ReadFrom.Configuration(app.Configuration)
    .CreateBootstrapLogger();  // logs DI exception messages


try
{
    Log.Information("Application Starting...");
    app.Run();
}
catch (Exception ex)
{
    Log.Error(ex, "Application stopped unexpectedly.");
}
finally
{
    Log.Information("Application stopped.");
    Log.CloseAndFlush();
}

// Make Program class public for integration tests
public partial class Program { }
