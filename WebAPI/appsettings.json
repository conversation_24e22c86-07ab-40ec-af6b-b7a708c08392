{"Logging": {"LogLevel": {"Default": "Error", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Dynamics": {"OrganizationURI": "", "TenantId": "", "ClientId": "", "SecretKey": "", "RedirectURI": ""}, "Swagger": {"UseSwagger": true, "UseSwaggerUI": true}, "Serilog": {"MinimumLevel": {"Default": "Error", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "ApplicationInsights", "Args": {"connectionString": "", "telemetryConverter": "Serilog.Sinks.ApplicationInsights.TelemetryConverters.TraceTelemetryConverter, Serilog.Sinks.ApplicationInsights"}}]}, "Users": [{"Id": "863183B2-A908-4B60-B705-EEB3078288B2", "Name": "Test", "ClientId": "test", "SecretKey": "test", "IsActive": true}]}