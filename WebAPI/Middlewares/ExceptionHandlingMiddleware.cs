﻿using Core.Exceptions;
using Core.Results;
using FluentValidation;
using Microsoft.PowerPlatform.Dataverse.Client.Utils;
using System.Net;
using System.Text;
using System.Text.Json;

namespace WebAPI.Middlewares
{
    /// <summary>
    /// Global exception handling middleware
    /// </summary>
    public class ExceptionHandlingMiddleware
    {
        private RequestDelegate _next;
        private readonly IWebHostEnvironment _env;
        private ILogger<ExceptionHandlingMiddleware> _logger;
        private readonly JsonSerializerOptions _jsonOption = new JsonSerializerOptions()
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        /// <summary>
        /// Exception handler constructor
        /// </summary>
        /// <param name="next"></param>
        /// <param name="env"></param>
        /// <param name="logger"></param>
        public ExceptionHandlingMiddleware(RequestDelegate next,
            IWebHostEnvironment env,
            ILogger<ExceptionHandlingMiddleware> logger)
        {
            _next = next;
            _env = env;
            _logger = logger;
        }

        /// <summary>
        /// Step
        /// </summary>
        /// <param name="httpContext"></param>
        /// <returns></returns>
        public async Task InvokeAsync(HttpContext httpContext)
        {
            try
            {
                await _next(httpContext);
            }
            catch (DataverseOperationException exception)
            {
                _logger.LogError(exception, exception.Message);
                httpContext.Response.ContentType = "application/json";
                httpContext.Response.StatusCode = (int)HttpStatusCode.BadRequest;

                await httpContext.Response.WriteAsync(JsonSerializer.Serialize(
                    new APIErrorResult(APIErrorType.BusinessError, exception.Message),
                    _jsonOption));
            }
            catch (BusinessException exception)
            {
                _logger.LogError(exception, exception.Message);
                httpContext.Response.ContentType = "application/json";
                httpContext.Response.StatusCode = (int)HttpStatusCode.BadRequest;

                await httpContext.Response.WriteAsync(JsonSerializer.Serialize(
                    new APIErrorResult(APIErrorType.BusinessError, exception.Message),
                    _jsonOption));
            }
            catch (AuthenticationException exception)
            {
                _logger.LogError(exception, exception.Message);
                httpContext.Response.ContentType = "application/json";
                httpContext.Response.StatusCode = (int)HttpStatusCode.Unauthorized;

                await httpContext.Response.WriteAsync(JsonSerializer.Serialize(
                    new APIErrorResult(APIErrorType.Authentication, exception.Message),
                    _jsonOption));
            }
            catch(ValidationException exception)
            {
                _logger.LogError(exception, exception.Message);
                httpContext.Response.ContentType = "application/json";
                httpContext.Response.StatusCode = (int)HttpStatusCode.BadRequest;

                await httpContext.Response.WriteAsync(JsonSerializer.Serialize(
                    new APIErrorResult(APIErrorType.ValidationError,
                    "One or more property are invalid.", 
                    exception.Errors.GroupBy(z => z.PropertyName).ToDictionary(z => z.Key, z => z.Select(b => b.ErrorMessage))),
                    _jsonOption));
            }
            catch (Exception exception)
            {

                _logger.LogError(exception, exception.Message);
                httpContext.Response.ContentType = "application/json";
                httpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;

                if (_env.EnvironmentName == "Development")
                    await httpContext.Response.WriteAsync(JsonSerializer.Serialize(
                        new APIErrorResult(APIErrorType.GeneralError, GetMessage(exception), GetStackTrace(exception)),
                        _jsonOption));
                else
                    await httpContext.Response.WriteAsync(JsonSerializer.Serialize(
                        new APIErrorResult(APIErrorType.GeneralError, GetMessage(exception)),
                        _jsonOption));
            }
        }

        private string GetMessage(Exception exception)
        {
            var sb = new StringBuilder();
            sb.AppendLine(exception.Message);
            
            if(exception.InnerException is not null)
            {
                sb.AppendLine("--------------------------------------------------");
                sb.AppendLine(GetMessage(exception.InnerException));
            }
            
            return sb.ToString();
        }

        private string GetStackTrace(Exception exception)
        {
            var sb = new StringBuilder();
            sb.AppendLine(exception.StackTrace);

            if (exception.InnerException is not null)
            {
                sb.AppendLine("--------------------------------------------------");
                sb.AppendLine(GetMessage(exception.InnerException));
            }

            return sb.ToString();
        }
    }
}

