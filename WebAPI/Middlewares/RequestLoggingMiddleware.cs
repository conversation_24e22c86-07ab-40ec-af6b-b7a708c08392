﻿using Serilog;

namespace WebAPI.Middlewares
{
    public class RequestLoggingMiddleware
    {
        private RequestDelegate _next;

        public RequestLoggingMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext httpContext)
        {
            var requestBody = await GetRequestBody(httpContext.Request);

            Log.ForContext("Body", requestBody)
                .Information("Info Log: {RequestMethod}:{RequestPath}", httpContext.Request.Method, httpContext.Request.Path);

            await _next(httpContext);
        }

        private async Task<string> GetRequestBody(HttpRequest request)
        {
            //if (AppSettings.IgnoreLogPaths != null && AppSettings.IgnoreLogPaths.Any(z => z.Contains(request.Path)))
            //    return "Request body does not loggable.";

            request.EnableBuffering();
            var requestBody = await new StreamReader(request.Body).ReadToEndAsync();
            request.Body.Position = 0;
            return requestBody;
        }
    }
}
