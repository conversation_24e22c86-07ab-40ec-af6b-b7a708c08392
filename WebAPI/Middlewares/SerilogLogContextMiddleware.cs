﻿using Serilog.Context;

namespace WebAPI.Middlewares
{
    public class SerilogLogContextMiddleware
    {
        private RequestDelegate _next;

        public SerilogLogContextMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext httpContext)
        {
            //if (httpContext.Items.ContainsKey("User"))
            //    LogContext.PushProperty("Username", (httpContext.Items["User"] as UsersConfig).Username);

            if (httpContext.Connection.RemoteIpAddress != null)
                LogContext.PushProperty("IpAddress", httpContext.Connection.RemoteIpAddress);

            LogContext.PushProperty("Headers", httpContext.Request.Headers.ToDictionary(h => h.Key, h => h.Value.ToString()), destructureObjects: true);
            LogContext.PushProperty("Schema", httpContext.Request.Scheme);
            LogContext.PushProperty("Host", httpContext.Request.Host);
            LogContext.PushProperty("Path", httpContext.Request.Path);
            LogContext.PushProperty("Method", httpContext.Request.Method);
            LogContext.PushProperty("QueryString", httpContext.Request.QueryString);

            await _next(httpContext);
        }
    }
}
