<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="FluentValidation" Version="11.2.2" />
		<PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="11.0.0" />
		<PackageReference Include="Microsoft.Dynamics.Sdk.Messages" Version="0.5.17" />
		<PackageReference Include="Microsoft.PowerPlatform.Dataverse.Client" Version="1.0.23" />
		<PackageReference Include="Microsoft.PowerPlatform.Dataverse.Client.Dynamics" Version="1.0.23" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Core\Core.csproj" />
	</ItemGroup>

</Project>
