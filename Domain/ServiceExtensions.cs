﻿using Core.Dynamics;
using Domain.Features.Message;
using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk;
using System.Reflection;
// This is a fourth tests commit for PR test bug fix
// Testing the cicd pipeline - test github pipeline
namespace Domain
{
    public static class ServiceExtensions
    {
        public static IServiceCollection AddDomainDependencies(this IServiceCollection services)
        {
            services
                .AddMediatR(Assembly.GetExecutingAssembly());

            services
                .AddSingleton<ServiceClientWrapper>(sp => new ServiceClientWrapper(sp.GetRequiredService<IConfiguration>()));
            services
                .AddTransient<IOrganizationService, ServiceClient>(sp => sp.GetRequiredService<ServiceClientWrapper>().ServiceClient.Clone());
            services
                .AddTransient<ServiceClient>(sp => sp.GetRequiredService<ServiceClientWrapper>().ServiceClient.Clone());
            services
                .AddScoped<MessageManager>();

            return services;
        }
    }
}
