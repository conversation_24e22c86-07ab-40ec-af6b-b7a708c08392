﻿using Core.Dynamics;
using Core.Exceptions;
using FluentValidation;
using MediatR;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using System.Text;

namespace Domain.Features.Message.Queries.GetAppointmentDetailById
{
    public class GetAppointmentDetailByIdHandler : IRequestHandler<GetAppointmentDetailByIdRequest, GetAppointmentDetailByIdResponse>
    {
        private readonly ServiceClient _service;
        private readonly IValidator<GetAppointmentDetailByIdRequest> _validator = new GetAppointmentDetailByIdRequestValidator();

        public GetAppointmentDetailByIdHandler(ServiceClient service)
        {
            _service = service;
        }

        public async Task<GetAppointmentDetailByIdResponse> Handle(GetAppointmentDetailByIdRequest request, CancellationToken cancellationToken)
        {
            _validator.ValidateAndThrow(request);

            var message = await GetMessageById(request.Id);
            if (message is null)
                throw new BusinessException($"There is no appointment by {request.Id}");

            var returnValue = new GetAppointmentDetailByIdResponse()
            {
                ActivityId = message.Id,
                Id = message.GetString("phcc_id"),
                AppointmentType = message.GetString("phcc_appointmenttype"),
                MessageId = message.GetString("phcc_messageid")
            };

            return returnValue;
        }

        private async Task<Entity> GetMessageById(Guid id)
        {
            var qe = new QueryExpression("phcc_message");
            qe.Criteria.AddCondition("activityid", ConditionOperator.Equal, id);
            qe.ColumnSet = new ColumnSet("phcc_id", "phcc_messageid", "phcc_appointmenttype");

            var entity = await _service.RetrieveFirstAsync(qe);
            return entity;
        }
    }
}
