﻿using Core.Constants;
using Core.Dynamics;
using Core.Exceptions;
using FluentValidation;
using MediatR;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;

namespace Domain.Features.Message.Commands.NegativeResult
{
    public class NegativeResultHandler : IRequestHandler<NegativeResultRequest, NegativeResultResponse>
    {
        private readonly ServiceClient _service;
        private readonly IValidator<NegativeResultRequest> _validator = new NegativeResultRequestValidator();
        private readonly MessageManager _messageManager;
        public NegativeResultHandler(ServiceClient service, MessageManager messageManager)
        {
            _service = service;
            _messageManager = messageManager;
        }

        public async Task<NegativeResultResponse> Handle(NegativeResultRequest request, CancellationToken cancellationToken)
        {
            _validator.ValidateAndThrow(request);

            var id = await CreateMessage(request);
            var message = _service.Retrieve("phcc_message", id, new ColumnSet("phcc_messageid"));

            var returnValue = new NegativeResultResponse()
            {
                Id = id,
                MessageId = message.GetString("phcc_messageid")
            };

            return returnValue;
        }

        private async Task<Guid> CreateMessage(NegativeResultRequest request)
        {
            var entity = new Entity("phcc_message");

            Entity? patient = _messageManager.GetPatientByHealthCardNumber(request.CustomIdentifier);
            string language = _messageManager.NationalityCheck(request.RecipientNationality);

            if (!string.IsNullOrEmpty(request.AppointmentLocation))
            {
                Entity? location = _messageManager.GetLocationByCode(request.AppointmentLocation);
                if (location == null)
                {
                    throw new BusinessException("Health centre does not exist on the lookup table for Cancer Screening SMS System.");
                }
                entity["phcc_locationid"] = new EntityReference(location.LogicalName, location.Id);
                entity["phcc_locationdescription"] = language == "English" ? location.GetString("msemr_text") : location.GetString("phcc_textarabic");
            }

            if (patient != null)
            {
                entity["regardingobjectid"] = new EntityReference(patient.LogicalName, patient.Id);
            }

            entity["phcc_id"] = request.AppointmentIdentifier;
            entity["phcc_appointmenttype"] = request.AppointmentType;
            if (request.AppointmentDateTime != null)
                entity["phcc_appointmentdatetime"] = request.AppointmentDateTime;
            entity["phcc_recipientfirstname"] = request.RecipientFirstName;
            entity["phcc_recipientlastname"] = request.RecipientLastName;
            entity["phcc_recipientphone"] = request.RecipientPhone;
            entity["phcc_emailaddress"] = request.RecipientEmail;
            if (request.SendDateTime != null)
                entity["phcc_scheduleddate"] = request.SendDateTime;
            entity["phcc_recipientnationality"] = language;
            entity["phcc_recipienthealthcardnumber"] = request.CustomIdentifier;
            entity["statecode"] = new OptionSetValue((int)ActivityStatus.Scheduled);
            entity["statuscode"] = new OptionSetValue((int)MessageStatus.WaitingSend);
            entity["phcc_messagetype"] = new OptionSetValue((int)MessageType.NegativeResult);

            var id = await _service.CreateAsync(entity);

            return id;
        }
    }
}
