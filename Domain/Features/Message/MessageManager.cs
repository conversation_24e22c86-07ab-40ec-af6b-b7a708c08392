﻿using Microsoft.Xrm.Sdk.Query;
using Microsoft.Xrm.Sdk;
using Microsoft.PowerPlatform.Dataverse.Client;
using Core.Dynamics;

namespace Domain.Features.Message
{
    public class MessageManager
    {
        private readonly ServiceClient _service;
        public MessageManager(ServiceClient service)
        {
            _service = service;
        }
        public string NationalityCheck(string nationality)
        {
            switch (nationality)
            {
                case "Bahraini":
                case "Egyptian":
                case "Iraqi":
                case "Jordanian":
                case "Kuwaiti":
                case "Lebanese":
                case "Mauritania":
                case "Moroccan":
                case "Omani":
                case "Palestinian":
                case "Qatar Document":
                case "Qatari":
                case "Romanian":
                case "Saudi":
                case "Sudanese":
                case "Syrian":
                case "Tunisian":
                case "Yemeni":
                    nationality = "Arabic";
                    break;
                default:
                    nationality = "English";
                    break;
            }
            return nationality;
        }

        public Entity? GetPatientByHealthCardNumber(string healthCardNumber)
        {
            QueryExpression patientQueryExpression = new QueryExpression();
            patientQueryExpression.EntityName = "contact";
            patientQueryExpression.NoLock = true;
            patientQueryExpression.Criteria.AddCondition("phcc_healthcardnumber", ConditionOperator.Equal, healthCardNumber);
            return _service.RetrieveFirst(patientQueryExpression);
        }

        public Entity? GetLocationByCode(string code)
        {
            QueryExpression locationQueryExpression = new QueryExpression();
            locationQueryExpression.EntityName = "msemr_codeableconcept";
            locationQueryExpression.NoLock = true;
            locationQueryExpression.Criteria.AddCondition("msemr_code", ConditionOperator.Equal, code);
            locationQueryExpression.Criteria.AddCondition("phcc_iscancerscreeninghc", ConditionOperator.Equal, true);
            locationQueryExpression.ColumnSet = new ColumnSet("msemr_text", "phcc_textarabic");
            return _service.RetrieveFirst(locationQueryExpression);
        }

        public bool IsAppointmentReminder(DateTime appointmentDateTime, int reminderSendHoursBefore)
        {
            DateTime date = appointmentDateTime.AddHours(-reminderSendHoursBefore);
            if (DateTime.Compare(date, TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, TimeZoneInfo.FindSystemTimeZoneById("Arab Standard Time"))) == 1)
            {
                return true;
            }
            return false;
        }
    }
}
