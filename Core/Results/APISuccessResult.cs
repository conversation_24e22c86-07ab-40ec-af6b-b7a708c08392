﻿using System.Text.Json.Serialization;

namespace Core.Results
{
    public class APISuccessResult<T> : IAPISuccessResult
    {
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string? Message { get; private set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public T? Data { get; private set; }

        public APISuccessResult() { }

        public APISuccessResult(T data) => Data = data;

        public APISuccessResult(string message) => Message = message;

        public APISuccessResult(T data, string message)
        {
            Data = data;
            Message = message;
        }        
    }
}