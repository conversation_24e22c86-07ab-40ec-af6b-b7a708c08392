﻿using Microsoft.Crm.Sdk.Messages;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using System.Text;

namespace Core.Dynamics
{
    public static class ServiceExtensions
    {
        public static Entity? RetrieveFirst(this IOrganizationService service, QueryExpression qe)
        {
            qe.NoLock = true;
            qe.PageInfo = null;
            qe.TopCount = 1;

            var entity = service.RetrieveMultiple(qe);
            if (entity.Entities.Any())
                return entity.Entities.First();
            return null;
        }

        public static async Task<Entity?> RetrieveFirstAsync(this ServiceClient service, QueryExpression qe)
        {
            qe.NoLock = true;
            qe.PageInfo = null;
            qe.TopCount = 1;

            var entity = await service.RetrieveMultipleAsync(qe);
            if (entity.Entities.Any())
                return entity.Entities.First();
            return null;
        }

        public static EntityCollection RetrieveAll(this IOrganizationService service, QueryExpression qe)
        {
            qe.NoLock = true;
            qe.TopCount = null;

            var ec = new EntityCollection();
            int pageNumber = 1;
            var moreRecord = true;
            while (moreRecord)
            {
                qe.PageInfo = new PagingInfo { Count = 5000, PageNumber = pageNumber };
                var _ec = service.RetrieveMultiple(qe);
                if (_ec.Entities.Any())
                    ec.Entities.AddRange(_ec.Entities);

                moreRecord = _ec.MoreRecords;
                pageNumber += 1;
            }

            return ec;
        }

        public static async Task<EntityCollection> RetrieveAllAsync(this ServiceClient service, QueryExpression qe)
        {
            qe.NoLock = true;
            qe.TopCount = null;

            var ec = new EntityCollection();
            int pageNumber = 1;
            var moreRecord = true;
            while (moreRecord)
            {
                qe.PageInfo = new PagingInfo { Count = 5000, PageNumber = pageNumber };
                var _ec = await service.RetrieveMultipleAsync(qe);
                if (_ec.Entities.Any())
                    ec.Entities.AddRange(_ec.Entities);

                moreRecord = _ec.MoreRecords;
                pageNumber += 1;
            }

            return ec;
        }

        public static bool Any(this IOrganizationService service, QueryExpression qe)
        {
            qe.NoLock = true;
            qe.TopCount = 1;
            qe.PageInfo = null;
            qe.ColumnSet = new ColumnSet(false);

            foreach (var link in qe.LinkEntities)
            {
                link.Columns = new ColumnSet(false);
                foreach (var deepLink in link.LinkEntities)
                {
                    deepLink.Columns = new ColumnSet(false);
                }
            }

            var ec = service.RetrieveMultiple(qe);
            return ec.Entities.Any();
        }

        public static async Task<bool> AnyAsync(this ServiceClient service, QueryExpression qe)
        {
            qe.NoLock = true;
            qe.TopCount = 1;
            qe.PageInfo = null;
            qe.ColumnSet = new ColumnSet(false);

            foreach (var link in qe.LinkEntities)
            {
                link.Columns = new ColumnSet(false);
                foreach (var deepLink in link.LinkEntities)
                    deepLink.Columns = new ColumnSet(false);
            }

            var ec = await service.RetrieveMultipleAsync(qe);
            return ec.Entities.Any();
        }

        public static bool Any(this IOrganizationService service, FetchExpression fe)
        {
            var ec = service.RetrieveMultiple(fe);
            return ec.Entities.Any();
        }

        public static async Task<bool> AnyAsync(this ServiceClient service, FetchExpression fe)
        {
            var ec = await service.RetrieveMultipleAsync(fe);
            return ec.Entities.Any();
        }

        public static void UploadFile(this ServiceClient service,
            string entityLogicalName, string attributeLogicalName, Guid recordId,
            string fileName, string mimeType, byte[] file)
        {
            var initRequest = new InitializeFileBlocksUploadRequest()
            {
                FileAttributeName = attributeLogicalName,
                FileName = fileName,
                Target = new EntityReference(entityLogicalName, recordId)
            };
            var initResponse = (InitializeFileBlocksUploadResponse)service.Execute(initRequest);

            var limit = 4194304;
            var fileContinuationToken = initResponse.FileContinuationToken;
            var blockIds = new List<string>();

            for (int i = 0; i < Math.Ceiling(file.Length / Convert.ToDecimal(limit)); i++)
            {
                var blockId = Convert.ToBase64String(Encoding.UTF8.GetBytes(Guid.NewGuid().ToString()));
                blockIds.Add(blockId);
                var blockData = file.Skip(i * limit).Take(limit).ToArray();
                var blockRequest = new UploadBlockRequest() { FileContinuationToken = fileContinuationToken, BlockId = blockId, BlockData = blockData };
                var blockResponse = (UploadBlockResponse)service.Execute(blockRequest);
            }

            var commitRequest = new CommitFileBlocksUploadRequest()
            {
                BlockList = blockIds.ToArray(),
                FileContinuationToken = fileContinuationToken,
                FileName = fileName,
                MimeType = mimeType
            };

            service.Execute(commitRequest);
        }

        public static async Task UploadFileAsync(this ServiceClient service,
            string entityLogicalName, string attributeLogicalName, Guid recordId,
            string fileName, string mimeType, byte[] file)
        {
            var initRequest = new InitializeFileBlocksUploadRequest()
            {
                FileAttributeName = attributeLogicalName,
                FileName = fileName,
                Target = new EntityReference(entityLogicalName, recordId)
            };
            var initResponse = (InitializeFileBlocksUploadResponse)await service.ExecuteAsync(initRequest);

            var limit = 4194304;
            var fileContinuationToken = initResponse.FileContinuationToken;
            var blockIds = new List<string>();

            for (int i = 0; i < Math.Ceiling(file.Length / Convert.ToDecimal(limit)); i++)
            {
                var blockId = Convert.ToBase64String(Encoding.UTF8.GetBytes(Guid.NewGuid().ToString()));
                blockIds.Add(blockId);
                var blockData = file.Skip(i * limit).Take(limit).ToArray();
                var blockRequest = new UploadBlockRequest() { FileContinuationToken = fileContinuationToken, BlockId = blockId, BlockData = blockData };
                var blockResponse = (UploadBlockResponse)await service.ExecuteAsync(blockRequest);
            }

            var commitRequest = new CommitFileBlocksUploadRequest()
            {
                BlockList = blockIds.ToArray(),
                FileContinuationToken = fileContinuationToken,
                FileName = fileName,
                MimeType = mimeType
            };

            await service.ExecuteAsync(commitRequest);
        }

        public static (string fileName, string mimeType, byte[] file) DownloadFile(this ServiceClient service,
            string entityLogicalName, string attributeLogicalName, Guid recordId)
        {
            var initRequest = new InitializeFileBlocksDownloadRequest()
            {
                FileAttributeName = attributeLogicalName,
                Target = new EntityReference(entityLogicalName, recordId)
            };
            var initResponse = (InitializeFileBlocksDownloadResponse)service.Execute(initRequest);

            var increment = 4194304;
            var from = 0;
            var fileSize = initResponse.FileSizeInBytes;
            byte[] downloaded = new byte[fileSize];
            var fileContinuationToken = initResponse.FileContinuationToken;

            while (from < fileSize)
            {
                var blockRequest = new DownloadBlockRequest()
                {
                    Offset = from,
                    BlockLength = increment,
                    FileContinuationToken = fileContinuationToken
                };
                var blockResponse = (DownloadBlockResponse)service.Execute(blockRequest);
                blockResponse.Data.CopyTo(downloaded, from);
                from += increment;
            }

            MimeTypes.TryGetMimeType(initResponse.FileName, out string mimeType);
            return (initResponse.FileName, mimeType, downloaded);
        }

        public static async Task<(string fileName, string mimeType, byte[] file)> DownloadFileAsync(this ServiceClient service,
            string entityLogicalName, string attributeLogicalName, Guid recordId)
        {
            var initRequest = new InitializeFileBlocksDownloadRequest()
            {
                FileAttributeName = attributeLogicalName,
                Target = new EntityReference(entityLogicalName, recordId)
            };
            var initResponse = (InitializeFileBlocksDownloadResponse)await service.ExecuteAsync(initRequest);

            var increment = 4194304;
            var from = 0;
            var fileSize = initResponse.FileSizeInBytes;
            byte[] downloaded = new byte[fileSize];
            var fileContinuationToken = initResponse.FileContinuationToken;

            while (from < fileSize)
            {
                var blockRequest = new DownloadBlockRequest()
                {
                    Offset = from,
                    BlockLength = increment,
                    FileContinuationToken = fileContinuationToken
                };
                var blockResponse = (DownloadBlockResponse)await service.ExecuteAsync(blockRequest);
                blockResponse.Data.CopyTo(downloaded, from);
                from += increment;
            }

            MimeTypes.TryGetMimeType(initResponse.FileName, out string mimeType);
            return (initResponse.FileName, mimeType, downloaded);
        }
    }
}
