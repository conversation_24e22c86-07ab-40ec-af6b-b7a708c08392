﻿using Microsoft.Extensions.Configuration;
using Microsoft.PowerPlatform.Dataverse.Client;

namespace Core.Dynamics
{
    public class ServiceClientWrapper
    {
        public readonly ServiceClient ServiceClient;

        public ServiceClientWrapper(IConfiguration configuration)
        {
            var organizationURI = new Uri(configuration.GetSection("Dynamics").GetValue<string>("OrganizationURI"));
            var clientId = configuration.GetSection("Dynamics").GetValue<string>("ClientId");
            var secretKey = configuration.GetSection("Dynamics").GetValue<string>("SecretKey");

            ServiceClient = new ServiceClient(organizationURI, clientId, secretKey, true);
        }
    }
}
