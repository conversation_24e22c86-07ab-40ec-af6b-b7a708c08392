# PHCC Cancer Screening SMS API - Complete Documentation

[![Cancer Screening CI/CD Pipeline](https://github.com/public-health-care-center-CORP/CancerScreeningSMS/actions/workflows/app-CancerScreeningSMS-ci.yml/badge.svg?branch=main)](https://github.com/public-health-care-center-CORP/CancerScreeningSMS/actions/workflows/app-CancerScreeningSMS-ci.yml)

## Table of Contents

1. [Project Overview](#project-overview)
2. [System Architecture](#system-architecture)
3. [Technology Stack](#technology-stack)
4. [API Documentation](#api-documentation)
5. [Business Logic](#business-logic)
6. [Configuration](#configuration)
7. [Development Setup](#development-setup)
8. [Deployment](#deployment)
9. [Testing](#testing)
10. [Monitoring & Logging](#monitoring--logging)
11. [Security](#security)
12. [Troubleshooting](#troubleshooting)

## Project Overview

### Purpose
The Cancer Screening SMS API is a specialized service designed to process SMS requests from upstream systems (primarily Fuji Synapse) for cancer screening appointments. The system manages appointment notifications, reminders, cancellations, and result communications through SMS messaging.

### Key Features
- **Appointment Management**: Create, reschedule, and cancel cancer screening appointments
- **SMS Notifications**: Automated SMS messaging for appointment confirmations and reminders
- **Multi-language Support**: English and Arabic language support based on patient nationality
- **Integration**: Seamless integration with Dynamics 365 Dataverse and Power Platform
- **Result Management**: Handle negative results and no-show notifications
- **Procedure Details**: Support for optional procedure names and codes (US3655 enhancement)

### Business Context
This API serves as a critical component in Qatar's Public Health Care Center (PHCC) cancer screening program, ensuring patients receive timely notifications about their screening appointments and results.

## System Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Fuji Synapse  │──▶│  Cancer SMS API │───▶│  Dynamics 365   │
│  (Upstream)     │    │                 │    │   Dataverse     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │ Power Platform  │
                       │ (Power Automate)│
                       └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Ooredoo SMS   │
                       │   API Service   │
                       └─────────────────┘
```

### Solution Structure

The solution follows Clean Architecture principles with three main projects:

#### 1. **WebAPI Layer** (`WebAPI` project)
- **Purpose**: Presentation layer handling HTTP requests and responses
- **Responsibilities**:
  - API controllers and routing
  - Request/response models
  - Authentication and authorization filters
  - Middleware pipeline
  - Swagger documentation

#### 2. **Domain Layer** (`Domain` project)
- **Purpose**: Business logic and domain models
- **Responsibilities**:
  - Command and query handlers (CQRS pattern)
  - Business rules and validation
  - Domain models and DTOs
  - MediatR request/response patterns

#### 3. **Core Layer** (`Core` project)
- **Purpose**: Shared infrastructure and utilities
- **Responsibilities**:
  - Configuration models
  - Constants and enums
  - Exception handling
  - Dynamics 365 integration utilities
  - Result patterns

### Design Patterns Used

1. **CQRS (Command Query Responsibility Segregation)**: Separates read and write operations
2. **Mediator Pattern**: Using MediatR for decoupled request handling
3. **Repository Pattern**: Abstracted data access through Dynamics 365 ServiceClient
4. **Dependency Injection**: Built-in .NET DI container
5. **Result Pattern**: Standardized API responses
6. **Validation Pattern**: FluentValidation for request validation

## Technology Stack

### Core Technologies
- **Language**: C# 10
- **Framework**: .NET 6.0 Web API
- **Architecture**: Clean Architecture with CQRS

### Key Dependencies

#### WebAPI Project
```xml
<PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />
<PackageReference Include="Microsoft.PowerPlatform.Dataverse.Client" Version="1.0.23" />
<PackageReference Include="Serilog.AspNetCore" Version="6.0.1" />
<PackageReference Include="Swashbuckle.AspNetCore" Version="6.2.3" />
```

#### Domain Project
```xml
<PackageReference Include="FluentValidation" Version="11.2.2" />
<PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="11.0.0" />
<PackageReference Include="Microsoft.PowerPlatform.Dataverse.Client" Version="1.0.23" />
```

#### Core Project
```xml
<PackageReference Include="Microsoft.PowerPlatform.Dataverse.Client" Version="1.0.23" />
<PackageReference Include="Microsoft.Dynamics.Sdk.Messages" Version="0.5.17" />
```

### External Dependencies
- **Dynamics 365 Dataverse**: Primary data storage and patient management
- **Power Automate**: Workflow automation (monitoring scope)
- **Ooredoo SMS API**: SMS delivery service (monitoring scope)
- **Application Insights**: Telemetry and monitoring
- **Serilog**: Structured logging

### Development Tools
- **IDE**: JetBrains Rider / Visual Studio 2022
- **Version Control**: Git
- **CI/CD**: GitHub Actions
- **Documentation**: Swagger/OpenAPI

## API Documentation

### Base URL
```
https://[your-domain]/api
```

### Authentication
All endpoints require header-based authentication:

```http
X-Client-Id: [client-id]
X-Secret-Key: [secret-key]
```

### Common Response Format

#### Success Response (201 Created)
```json
{
  "isSuccess": true,
  "data": {
    "id": "guid",
    "messageId": "string"
  },
  "message": null,
  "errors": null
}
```

#### Error Response (400 Bad Request)
```json
{
  "isSuccess": false,
  "data": null,
  "message": "Validation failed",
  "errors": [
    {
      "field": "AppointmentIdentifier",
      "message": "AppointmentIdentifier can not be empty."
    }
  ]
}
```

### Endpoints

#### 1. Create New Appointment
**POST** `/api/appointment/new`

Creates a new cancer screening appointment and schedules SMS notifications.

**Request Body:**
```json
{
  "appointmentIdentifier": "12345678",
  "appointmentType": "Breast Screening",
  "procedureName": "Screening Mammogram",
  "procedureCode": "77067",
  "appointmentLocation": "RAK",
  "appointmentLocationDescription": "Rawdat Al Khail",
  "appointmentDateTime": "2025-04-30T10:00:00",
  "appointmentArriveMinutesBefore": 30,
  "customIdentifier": "HC00000001",
  "subjectIdentifier": "HC00000001",
  "recipientFirstName": "John",
  "recipientLastName": "Doe",
  "recipientPhone": "12345678",
  "recipientEmail": "<EMAIL>",
  "sendDateTime": "2025-04-28T12:48:50",
  "reminderSendHoursBefore": 24,
  "reminderType": "Screening",
  "recipientNationality": "American"
}
```

**Field Descriptions:**
- `appointmentIdentifier`: Unique identifier for the appointment
- `procedureName`: Optional procedure name (max 200 chars) - *New in US3655*
- `procedureCode`: Optional procedure code (max 50 chars) - *New in US3655*
- `customIdentifier`: Health card number (must be 10 chars, start with "HC")
- `appointmentDateTime`: Must be in the future
- `sendDateTime`: Must be before appointment date
- `recipientNationality`: Determines message language (Arabic/English)

#### 2. Reschedule Appointment
**POST** `/api/appointment/reschedule`

Reschedules an existing appointment and updates notifications.

**Request Body:** (Same structure as new appointment)

#### 3. Cancel Appointment
**POST** `/api/appointment/cancel`

Cancels an existing appointment and sends cancellation notification.

**Request Body:**
```json
{
  "customIdentifier": "HC00000001",
  "appointmentIdentifier": "12345678",
  "appointmentType": "Breast Screening",
  "appointmentLocation": "RAK",
  "appointmentLocationDescription": "Rawdat Al Khail",
  "appointmentDateTime": "2025-04-30T10:00:00",
  "subjectIdentifier": "HC00000001",
  "recipientFirstName": "John",
  "recipientLastName": "Doe",
  "recipientPhone": "12345678",
  "recipientEmail": "<EMAIL>",
  "sendDateTime": "2025-04-28T12:48:50",
  "recipientNationality": "American"
}
```

#### 4. Negative Result Notification
**POST** `/api/appointment/negative-result`

Sends notification for negative screening results.

#### 5. No-Show Notification
**POST** `/api/appointment/no-show`

Sends notification when patient doesn't show up for appointment.

### Validation Rules

#### Common Validations
- **appointmentIdentifier**: Required, not empty
- **appointmentType**: Required, not empty
- **customIdentifier**: Required, exactly 10 characters, must start with "HC"
- **appointmentDateTime**: Required, must be in the future
- **sendDateTime**: Required, must be before appointment date

#### New Field Validations (US3655)
- **procedureName**: Optional, max 200 characters
- **procedureCode**: Optional, max 50 characters

### Error Codes

| HTTP Status | Description | Common Causes |
|-------------|-------------|---------------|
| 400 | Bad Request | Validation errors, invalid data format |
| 401 | Unauthorized | Missing or invalid authentication headers |
| 409 | Conflict | Duplicate appointment identifier, active appointment exists |
| 500 | Internal Server Error | Database connection issues, external service failures |

## Business Logic

### Message Types
The system handles five types of messages:

1. **NewAppointment**: Initial appointment confirmation
2. **AppointmentReminder**: Automatic reminder before appointment
3. **RescheduleAppointment**: Appointment time change notification
4. **CancelAppointment**: Appointment cancellation notification
5. **NegativeResult**: Screening result notification
6. **NoShow**: No-show follow-up notification

### Message Workflow

```mermaid
graph TD
    A[API Request] --> B[Validation]
    B --> C{Valid?}
    C -->|No| D[Return 400 Error]
    C -->|Yes| E[Check Business Rules]
    E --> F{Rules Pass?}
    F -->|No| G[Return 409 Conflict]
    F -->|Yes| H[Create Message in Dynamics]
    H --> I{Reminder Needed?}
    I -->|Yes| J[Create Reminder Message]
    I -->|No| K[Return Success]
    J --> K
```

### Language Selection Logic
The system determines message language based on patient nationality:

**Arabic Languages:**
- Bahraini, Egyptian, Iraqi, Jordanian, Kuwaiti, Lebanese
- Mauritania, Moroccan, Omani, Palestinian, Qatari
- Romanian, Saudi, Sudanese, Syrian, Tunisian, Yemeni

**Default:** English for all other nationalities

### Business Rules

#### New Appointment Rules
1. Patient cannot have multiple active appointments
2. Appointment identifier must be unique
3. Appointment date must be in the future
4. Send date must be before appointment date
5. Health card number must exist in patient database

#### Reminder Logic
Automatic reminders are created when:
- `reminderSendHoursBefore` > 0
- Appointment date allows for reminder scheduling
- Reminder send time = appointment time - reminder hours

#### Location Validation
- Appointment location must exist in the lookup table
- Location descriptions are provided in both English and Arabic

## Configuration

### Application Settings

#### appsettings.json Structure
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Error",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "Dynamics": {
    "OrganizationURI": "[Dynamics 365 URL]",
    "TenantId": "[Azure AD Tenant ID]",
    "ClientId": "[App Registration Client ID]",
    "SecretKey": "[App Registration Secret]",
    "RedirectURI": "[Redirect URI]"
  },
  "Swagger": {
    "UseSwagger": true,
    "UseSwaggerUI": true
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Error",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console"
      },
      {
        "Name": "ApplicationInsights",
        "Args": {
          "connectionString": "[Application Insights Connection String]",
          "telemetryConverter": "Serilog.Sinks.ApplicationInsights.TelemetryConverters.TraceTelemetryConverter, Serilog.Sinks.ApplicationInsights"
        }
      }
    ]
  },
  "Users": [
    {
      "Id": "863183B2-A908-4B60-B705-EEB3078288B2",
      "Name": "Test",
      "ClientId": "test",
      "SecretKey": "test",
      "IsActive": true
    }
  ]
}
```

#### Configuration Sections

**Dynamics Configuration:**
- `OrganizationURI`: Dynamics 365 organization URL
- `TenantId`: Azure AD tenant identifier
- `ClientId`: Service principal client ID
- `SecretKey`: Service principal secret
- `RedirectURI`: OAuth redirect URI

**User Authentication:**
- Multiple users can be configured
- Each user has unique ClientId/SecretKey combination
- Users can be activated/deactivated via `IsActive` flag

**Logging Configuration:**
- Structured logging with Serilog
- Application Insights integration
- Console output for development
- Configurable log levels per namespace

### Environment Variables
For production deployments, sensitive values should be stored as environment variables:

```bash
DYNAMICS__ORGANIZATIONURI=https://yourorg.crm4.dynamics.com/
DYNAMICS__TENANTID=your-tenant-id
DYNAMICS__CLIENTID=your-client-id
DYNAMICS__SECRETKEY=your-secret-key
APPLICATIONINSIGHTS__CONNECTIONSTRING=your-connection-string
```

## Development Setup

### Prerequisites
- **.NET 6.0 SDK** or later
- **Visual Studio 2022** or **JetBrains Rider**
- **Git** for version control
- **Dynamics 365** environment access
- **Azure subscription** (for Application Insights)

### Local Development Setup

#### 1. Clone Repository
```bash
git clone https://github.com/public-health-care-center-CORP/CancerScreeningSMS.git
cd CancerScreeningSMS
```

#### 2. Restore Dependencies
```bash
dotnet restore
```

#### 3. Configure Settings
1. Copy `appsettings.json` to `appsettings.Development.json`
2. Update Dynamics 365 connection settings
3. Configure Application Insights connection string
4. Set up test user credentials

#### 4. Build Solution
```bash
dotnet build
```

#### 5. Run Application
```bash
cd WebAPI
dotnet run
```

The API will be available at:
- HTTP: `http://localhost:5000`
- HTTPS: `https://localhost:5001`
- Swagger UI: `https://localhost:5001/swagger`

### Project Structure
```
CancerScreeningSMS/
├── Core/                           # Shared infrastructure
│   ├── Configurations/            # Configuration models
│   ├── Constants/                 # Enums and constants
│   ├── Contracts/                 # Shared contracts
│   ├── Dynamics/                  # Dynamics 365 utilities
│   ├── Exceptions/                # Custom exceptions
│   ├── Extensions/                # Extension methods
│   ├── Models/                    # Shared models
│   └── Results/                   # API result patterns
├── Domain/                        # Business logic layer
│   ├── Features/                  # Feature-based organization
│   │   └── Message/              # Message handling features
│   │       ├── Commands/         # CQRS commands
│   │       ├── Queries/          # CQRS queries
│   │       └── MessageManager.cs # Core business logic
│   └── ServiceExtensions.cs      # DI configuration
├── WebAPI/                        # Presentation layer
│   ├── Controllers/              # API controllers
│   ├── Filters/                  # Action filters
│   ├── Middlewares/              # Custom middleware
│   ├── Setup/                    # Startup configuration
│   ├── Program.cs                # Application entry point
│   └── appsettings.json          # Configuration
└── PHCC.Notifier.sln             # Solution file
```

### Development Guidelines

#### Code Style
- Follow C# coding conventions
- Use meaningful variable and method names
- Implement proper error handling
- Add XML documentation for public APIs
- Use async/await for I/O operations

#### Testing Strategy
- Unit tests for business logic
- Integration tests for API endpoints
- Mock external dependencies (Dynamics 365)
- Test validation rules thoroughly

#### Git Workflow
- Create feature branches from `main`
- Use descriptive commit messages
- Submit pull requests for code review
- Ensure CI/CD pipeline passes

## Deployment

### Azure App Service Deployment

#### 1. Create App Service
```bash
az webapp create \
  --resource-group rg-phcc-cancer-sms \
  --plan asp-phcc-cancer-sms \
  --name app-cancer-sms-api \
  --runtime "DOTNETCORE|6.0"
```

#### 2. Configure Application Settings
```bash
az webapp config appsettings set \
  --resource-group rg-phcc-cancer-sms \
  --name app-cancer-sms-api \
  --settings \
    DYNAMICS__ORGANIZATIONURI="https://yourorg.crm4.dynamics.com/" \
    DYNAMICS__TENANTID="your-tenant-id" \
    DYNAMICS__CLIENTID="your-client-id" \
    DYNAMICS__SECRETKEY="your-secret-key"
```

#### 3. Deploy Application
```bash
dotnet publish -c Release -o ./publish
az webapp deployment source config-zip \
  --resource-group rg-phcc-cancer-sms \
  --name app-cancer-sms-api \
  --src ./publish.zip
```

### CI/CD Pipeline

The project includes GitHub Actions workflow for automated deployment:

**Workflow File:** `.github/workflows/app-CancerScreeningSMS-ci.yml`

**Pipeline Steps:**
1. **Build**: Restore dependencies and compile
2. **Test**: Run unit and integration tests
3. **Security Scan**: Code security analysis
4. **Deploy**: Deploy to Azure App Service
5. **Health Check**: Verify deployment success

### Environment Configuration

#### Development Environment
- Local Dynamics 365 sandbox
- Console logging enabled
- Swagger UI enabled
- Detailed error messages

#### Staging Environment
- Staging Dynamics 365 environment
- Application Insights logging
- Limited Swagger access
- Sanitized error messages

#### Production Environment
- Production Dynamics 365 environment
- Application Insights only
- Swagger disabled
- Generic error messages
- Enhanced security headers

## Domain Models and Business Logic

### Domain Layer Architecture

The Domain layer implements the CQRS (Command Query Responsibility Segregation) pattern using MediatR:

```
Domain/
├── Features/
│   └── Message/
│       ├── Commands/              # Write operations
│       │   ├── NewAppointment/
│       │   ├── RescheduleAppointment/
│       │   ├── CancelAppointment/
│       │   ├── NegativeResult/
│       │   └── NoShow/
│       ├── Queries/               # Read operations
│       └── MessageManager.cs      # Core business logic
└── ServiceExtensions.cs           # Dependency injection
```

### Command Structure

Each command follows a consistent pattern:

#### 1. Request Model
```csharp
public class NewAppointmentRequest : IRequest<NewAppointmentResponse>
{
    public string AppointmentIdentifier { get; set; }
    public string AppointmentType { get; set; }
    public string ProcedureName { get; set; }        // Optional (US3655)
    public string ProcedureCode { get; set; }        // Optional (US3655)
    // ... other properties
}
```

#### 2. Response Model
```csharp
public class NewAppointmentResponse
{
    public Guid Id { get; set; }
    public string MessageId { get; set; }
}
```

#### 3. Validator
```csharp
public class NewAppointmentRequestValidator : AbstractValidator<NewAppointmentRequest>
{
    public NewAppointmentRequestValidator()
    {
        RuleFor(x => x.AppointmentIdentifier)
            .NotEmpty()
            .WithMessage("AppointmentIdentifier can not be empty.");

        RuleFor(x => x.ProcedureName)
            .MaximumLength(200)
            .WithMessage("ProcedureName cannot exceed 200 characters.");

        RuleFor(x => x.ProcedureCode)
            .MaximumLength(50)
            .WithMessage("ProcedureCode cannot exceed 50 characters.");
    }
}
```

#### 4. Handler
```csharp
public class NewAppointmentHandler : IRequestHandler<NewAppointmentRequest, NewAppointmentResponse>
{
    private readonly ServiceClient _service;
    private readonly IValidator<NewAppointmentRequest> _validator;
    private readonly MessageManager _messageManager;

    public async Task<NewAppointmentResponse> Handle(NewAppointmentRequest request, CancellationToken cancellationToken)
    {
        // 1. Validate request
        _validator.ValidateAndThrow(request);

        // 2. Apply business rules
        await ValidateBusinessRules(request);

        // 3. Create message in Dynamics
        var id = await CreateMessage(request, MessageType.NewAppointment);

        // 4. Create reminder if needed
        if (_messageManager.IsAppointmentReminder(request.AppointmentDateTime, request.ReminderSendHoursBefore))
        {
            await CreateMessage(request, MessageType.AppointmentReminder);
        }

        return new NewAppointmentResponse { Id = id, MessageId = request.AppointmentIdentifier };
    }
}
```

### Message Manager

The `MessageManager` class contains core business logic:

#### Key Methods

**1. Nationality Language Mapping**
```csharp
public string NationalityCheck(string nationality)
{
    // Maps specific nationalities to Arabic, defaults to English
    switch (nationality)
    {
        case "Bahraini":
        case "Egyptian":
        case "Iraqi":
        // ... other Arabic nationalities
            return "Arabic";
        default:
            return "English";
    }
}
```

**2. Patient Lookup**
```csharp
public Entity? GetPatientByHealthCardNumber(string healthCardNumber)
{
    // Queries Dynamics 365 for patient by health card number
    // Returns null if patient not found
}
```

**3. Location Validation**
```csharp
public Entity? GetLocationByCode(string locationCode)
{
    // Validates location exists in lookup table
    // Returns location entity with English/Arabic descriptions
}
```

**4. Reminder Logic**
```csharp
public bool IsAppointmentReminder(DateTime appointmentDateTime, int reminderHoursBefore)
{
    // Determines if reminder should be created
    // Based on appointment time and reminder hours
}
```

### Data Mapping to Dynamics 365

#### Message Entity Fields
```csharp
entity["phcc_id"] = request.AppointmentIdentifier;
entity["phcc_appointmenttype"] = request.AppointmentType;
entity["phcc_procedurename"] = request.ProcedureName;      // New field (US3655)
entity["phcc_procedurecode"] = request.ProcedureCode;      // New field (US3655)
entity["phcc_locationid"] = new EntityReference(location.LogicalName, location.Id);
entity["phcc_locationdescription"] = language == "English" ?
    location.GetString("msemr_text") :
    location.GetString("phcc_textarabic");
entity["phcc_appointmentdatetime"] = request.AppointmentDateTime;
entity["phcc_arriveminutesbefore"] = request.AppointmentArriveMinutesBefore;
entity["phcc_recipientfirstname"] = request.RecipientFirstName;
entity["phcc_recipientlastname"] = request.RecipientLastName;
entity["phcc_recipientphone"] = request.RecipientPhone;
entity["phcc_emailaddress"] = request.RecipientEmail;
entity["phcc_scheduleddate"] = calculatedSendDateTime;
entity["phcc_reminderhoursbefore"] = request.ReminderSendHoursBefore;
entity["phcc_recipientnationality"] = language;
entity["phcc_recipienthealthcardnumber"] = request.CustomIdentifier;
entity["statecode"] = new OptionSetValue((int)ActivityStatus.Scheduled);
entity["statuscode"] = new OptionSetValue((int)MessageStatus.WaitingSend);
entity["phcc_messagetype"] = new OptionSetValue((int)messageType);
```

### Business Rules Implementation

#### 1. Duplicate Prevention
```csharp
// Check for existing active appointments
if (GetActiveMessages(request.CustomIdentifier).Result.Count > 0)
{
    throw new BusinessException("There is also an active upcoming appointment for the patient. You can only reschedule or cancel this meeting.");
}

// Check for duplicate appointment identifier
if (IsExistAppointmentMessage(request.AppointmentIdentifier))
{
    throw new BusinessException("There is already a record for this appointment identitifer.");
}
```

#### 2. Location Validation
```csharp
Entity? location = _messageManager.GetLocationByCode(request.AppointmentLocation);
if (location == null)
{
    throw new BusinessException("Health centre does not exist on the lookup table for Cancer Screening SMS System.");
}
```

#### 3. Patient Association
```csharp
Entity? patient = _messageManager.GetPatientByHealthCardNumber(request.CustomIdentifier);
if (patient != null)
{
    entity["regardingobjectid"] = new EntityReference(patient.LogicalName, patient.Id);
}
```

### Constants and Enums

#### Message Types
```csharp
public enum MessageType
{
    NewAppointment = 1,
    AppointmentReminder = 2,
    RescheduleAppointment = 3,
    CancelAppointment = 4,
    NegativeResult = 5,
    NoShow = 6
}
```

#### Activity Status
```csharp
public enum ActivityStatus
{
    Open = 0,
    Completed = 1,
    Cancelled = 2,
    Scheduled = 3
}
```

#### Message Status
```csharp
public enum MessageStatus
{
    WaitingSend = 1,
    Sent = 2,
    Failed = 3,
    Cancelled = 4
}
```

### Error Handling

#### Custom Exceptions
```csharp
public class BusinessException : Exception
{
    public BusinessException(string message) : base(message) { }
}

public class AuthenticationException : Exception
{
    public AuthenticationException(string message) : base(message) { }
}
```

#### Exception Middleware
The `ExceptionHandlingMiddleware` provides centralized error handling:
- Catches all unhandled exceptions
- Logs errors with structured logging
- Returns appropriate HTTP status codes
- Sanitizes error messages for production

## Testing

### Testing Strategy

The project follows a comprehensive testing approach:

#### 1. Unit Testing
**Focus Areas:**
- Business logic validation
- Domain model behavior
- Validation rules
- Message manager functionality

**Example Test Structure:**
```csharp
[Test]
public void NewAppointmentValidator_Should_Fail_When_AppointmentIdentifier_IsEmpty()
{
    // Arrange
    var request = new NewAppointmentRequest { AppointmentIdentifier = "" };
    var validator = new NewAppointmentRequestValidator();

    // Act
    var result = validator.Validate(request);

    // Assert
    Assert.IsFalse(result.IsValid);
    Assert.Contains("AppointmentIdentifier can not be empty.",
        result.Errors.Select(e => e.ErrorMessage));
}
```

#### 2. Integration Testing
**Focus Areas:**
- API endpoint functionality
- Database integration
- External service mocking
- End-to-end workflows

**Example Integration Test:**
```csharp
[Test]
public async Task POST_NewAppointment_Should_Return_201_When_Valid_Request()
{
    // Arrange
    var client = _factory.CreateClient();
    client.DefaultRequestHeaders.Add("X-Client-Id", "test");
    client.DefaultRequestHeaders.Add("X-Secret-Key", "test");

    var request = new NewAppointmentRequest
    {
        AppointmentIdentifier = "TEST123",
        AppointmentType = "Breast Screening",
        // ... other required fields
    };

    // Act
    var response = await client.PostAsJsonAsync("/api/appointment/new", request);

    // Assert
    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
}
```

#### 3. API Testing Scripts

**PowerShell Test Script:** `test-api-procedure-fields.ps1`
```powershell
# Test new appointment with procedure fields
$headers = @{
    "X-Client-Id" = "test"
    "X-Secret-Key" = "test"
    "Content-Type" = "application/json"
}

$body = @{
    appointmentIdentifier = "TEST001"
    appointmentType = "Breast Screening"
    procedureName = "Screening Mammogram"
    procedureCode = "77067"
    # ... other fields
} | ConvertTo-Json

Invoke-RestMethod -Uri "https://localhost:5001/api/appointment/new" -Method POST -Headers $headers -Body $body
```

### Test Data Management

#### Test Scenarios
1. **Valid Requests**
   - Complete appointment data
   - Optional procedure fields
   - Different nationalities
   - Various appointment types

2. **Validation Errors**
   - Missing required fields
   - Invalid field lengths
   - Invalid date formats
   - Invalid health card numbers

3. **Business Rule Violations**
   - Duplicate appointment identifiers
   - Multiple active appointments
   - Invalid location codes
   - Past appointment dates

#### Mock Data Setup
```csharp
public static class TestData
{
    public static NewAppointmentRequest ValidNewAppointment => new()
    {
        AppointmentIdentifier = "TEST001",
        AppointmentType = "Breast Screening",
        ProcedureName = "Screening Mammogram",
        ProcedureCode = "77067",
        AppointmentLocation = "RAK",
        AppointmentLocationDescription = "Rawdat Al Khail",
        AppointmentDateTime = DateTime.Now.AddDays(7),
        AppointmentArriveMinutesBefore = 30,
        CustomIdentifier = "HC00000001",
        SubjectIdentifier = "HC00000001",
        RecipientFirstName = "John",
        RecipientLastName = "Doe",
        RecipientPhone = "12345678",
        RecipientEmail = "<EMAIL>",
        SendDateTime = DateTime.Now.AddDays(5),
        ReminderSendHoursBefore = 24,
        ReminderType = "Screening",
        RecipientNationality = "American"
    };
}
```

### Running Tests

#### Local Testing
```bash
# Run all tests
dotnet test

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"

# Run specific test category
dotnet test --filter Category=Unit

# Run integration tests
dotnet test --filter Category=Integration
```

#### CI/CD Testing
Tests are automatically executed in the GitHub Actions pipeline:
- Unit tests run on every pull request
- Integration tests run on main branch commits
- Code coverage reports are generated
- Test results are published to GitHub

## Monitoring & Logging

### Structured Logging with Serilog

#### Log Configuration
```json
{
  "Serilog": {
    "MinimumLevel": {
      "Default": "Error",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console"
      },
      {
        "Name": "ApplicationInsights",
        "Args": {
          "connectionString": "[Connection String]",
          "telemetryConverter": "Serilog.Sinks.ApplicationInsights.TelemetryConverters.TraceTelemetryConverter, Serilog.Sinks.ApplicationInsights"
        }
      }
    ]
  }
}
```

#### Log Enrichment
```csharp
Log.Logger = new LoggerConfiguration()
    .Destructure.UsingAttributes()
    .Enrich.FromLogContext()
    .Enrich.WithProperty("Application", "PHCC.SMS")
    .ReadFrom.Configuration(app.Configuration)
    .CreateBootstrapLogger();
```

#### Request Logging Middleware
```csharp
public class RequestLoggingMiddleware
{
    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        using (LogContext.PushProperty("RequestId", Guid.NewGuid()))
        using (LogContext.PushProperty("RequestPath", context.Request.Path))
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                await next(context);
            }
            finally
            {
                stopwatch.Stop();
                Log.Information("Request completed in {ElapsedMilliseconds}ms",
                    stopwatch.ElapsedMilliseconds);
            }
        }
    }
}
```

### Application Insights Integration

#### Telemetry Collection
- **Request Telemetry**: HTTP request/response data
- **Dependency Telemetry**: Dynamics 365 API calls
- **Exception Telemetry**: Unhandled exceptions
- **Custom Events**: Business events and metrics
- **Performance Counters**: System performance data

#### Custom Telemetry
```csharp
public class NewAppointmentHandler : IRequestHandler<NewAppointmentRequest, NewAppointmentResponse>
{
    private readonly TelemetryClient _telemetryClient;

    public async Task<NewAppointmentResponse> Handle(NewAppointmentRequest request, CancellationToken cancellationToken)
    {
        using var activity = _telemetryClient.StartOperation<RequestTelemetry>("NewAppointment");

        try
        {
            // Business logic
            var result = await ProcessAppointment(request);

            _telemetryClient.TrackEvent("AppointmentCreated", new Dictionary<string, string>
            {
                ["AppointmentType"] = request.AppointmentType,
                ["Location"] = request.AppointmentLocation,
                ["HasProcedureDetails"] = (!string.IsNullOrEmpty(request.ProcedureName)).ToString()
            });

            return result;
        }
        catch (Exception ex)
        {
            _telemetryClient.TrackException(ex);
            throw;
        }
    }
}
```

### Health Checks

#### Endpoint Configuration
```csharp
builder.Services.AddHealthChecks()
    .AddCheck<DynamicsHealthCheck>("dynamics")
    .AddCheck<DatabaseHealthCheck>("database");

app.MapHealthChecks("/health", new HealthCheckOptions
{
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});
```

#### Custom Health Checks
```csharp
public class DynamicsHealthCheck : IHealthCheck
{
    private readonly ServiceClient _serviceClient;

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            // Test Dynamics connection
            var result = await _serviceClient.RetrieveAsync("organization", Guid.Empty, new ColumnSet("name"));
            return HealthCheckResult.Healthy("Dynamics 365 connection is healthy");
        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy("Dynamics 365 connection failed", ex);
        }
    }
}
```

### Monitoring Dashboards

#### Key Metrics to Monitor
1. **Request Metrics**
   - Request rate (requests/minute)
   - Response time (95th percentile)
   - Error rate (4xx/5xx responses)
   - Success rate

2. **Business Metrics**
   - Appointments created per hour
   - Message types distribution
   - Location usage statistics
   - Language preference distribution

3. **System Metrics**
   - CPU utilization
   - Memory usage
   - Dynamics 365 API call latency
   - Database connection pool usage

#### Alert Configuration
```json
{
  "alerts": [
    {
      "name": "High Error Rate",
      "condition": "requests/failed > 10 in 5 minutes",
      "severity": "Critical"
    },
    {
      "name": "Slow Response Time",
      "condition": "requests/duration > 5000ms (95th percentile)",
      "severity": "Warning"
    },
    {
      "name": "Dynamics Connection Failure",
      "condition": "dependencies/failed where target contains 'dynamics'",
      "severity": "Critical"
    }
  ]
}
```

## Security

### Authentication & Authorization

#### API Key Authentication
The API uses header-based authentication with client credentials:

```http
X-Client-Id: [unique-client-identifier]
X-Secret-Key: [secret-key]
```

**Implementation:**
```csharp
public class AuthorizeFilter : Attribute, IAuthorizationFilter
{
    public void OnAuthorization(AuthorizationFilterContext context)
    {
        // Validate X-Client-Id header
        if (!context.HttpContext.Request.Headers.TryGetValue("X-Client-Id", out var clientId))
            throw new AuthenticationException("X-Client-Id parameter is missing.");

        // Validate X-Secret-Key header
        if (!context.HttpContext.Request.Headers.TryGetValue("X-Secret-Key", out var secretKey))
            throw new AuthenticationException("X-Secret-Key parameter is missing.");

        // Verify credentials against configuration
        var users = _configuration.GetSection("Users").Get<IEnumerable<UserConfig>>();
        var user = users.FirstOrDefault(u => u.ClientId == clientId && u.SecretKey == secretKey);

        if (user == null || !user.IsActive)
            throw new AuthenticationException("Invalid credentials or inactive account.");
    }
}
```

#### User Management
```csharp
public class UserConfig
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string ClientId { get; set; }
    public string SecretKey { get; set; }
    public bool IsActive { get; set; }
}
```

### Security Headers

#### Security Headers Middleware
```csharp
public class SecurityHeadersMiddleware
{
    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        // Add security headers
        context.Response.Headers.Add("X-Content-Type-Options", "nosniff");
        context.Response.Headers.Add("X-Frame-Options", "DENY");
        context.Response.Headers.Add("X-XSS-Protection", "1; mode=block");
        context.Response.Headers.Add("Referrer-Policy", "strict-origin-when-cross-origin");
        context.Response.Headers.Add("Content-Security-Policy", "default-src 'self'");

        await next(context);
    }
}
```

### Data Protection

#### Sensitive Data Handling
- **PII Encryption**: Patient data encrypted in transit and at rest
- **Secrets Management**: Azure Key Vault for production secrets
- **Data Minimization**: Only necessary data is collected and stored
- **Audit Logging**: All data access is logged for compliance

#### HTTPS Enforcement
```csharp
// Force HTTPS redirection
app.UseHttpsRedirection();

// HSTS for production
if (!app.Environment.IsDevelopment())
{
    app.UseHsts();
}
```

### Dynamics 365 Security

#### Service Principal Authentication
```json
{
  "Dynamics": {
    "OrganizationURI": "https://yourorg.crm4.dynamics.com/",
    "TenantId": "[Azure AD Tenant ID]",
    "ClientId": "[Service Principal Client ID]",
    "SecretKey": "[Service Principal Secret]"
  }
}
```

#### Least Privilege Access
- Service principal has minimal required permissions
- Read/write access only to specific entities
- No administrative privileges
- Regular access review and rotation

### Security Best Practices

#### 1. Input Validation
- All inputs validated using FluentValidation
- SQL injection prevention through parameterized queries
- XSS prevention through output encoding
- File upload restrictions (if applicable)

#### 2. Error Handling
- Generic error messages in production
- Detailed errors only in development
- No sensitive information in error responses
- Centralized exception handling

#### 3. Logging Security
- No sensitive data in logs
- Structured logging for security events
- Log retention policies
- Access control for log data

## Troubleshooting

### Common Issues

#### 1. Authentication Failures

**Symptoms:**
- 401 Unauthorized responses
- "X-Client-Id parameter is missing" errors
- "Invalid credentials" messages

**Solutions:**
```bash
# Check headers are included
curl -H "X-Client-Id: test" -H "X-Secret-Key: test" https://api-url/api/appointment/new

# Verify user configuration
# Check appsettings.json Users section
# Ensure IsActive = true
# Verify ClientId/SecretKey match exactly
```

#### 2. Dynamics 365 Connection Issues

**Symptoms:**
- "Unable to connect to Dynamics" errors
- Timeout exceptions
- Authentication failures to Dynamics

**Solutions:**
```csharp
// Check connection string format
"OrganizationURI": "https://yourorg.crm4.dynamics.com/"  // Note trailing slash

// Verify service principal permissions
// Ensure client ID and secret are correct
// Check Azure AD app registration

// Test connection manually
var serviceClient = new ServiceClient(connectionString);
var isReady = serviceClient.IsReady;
```

#### 3. Validation Errors

**Symptoms:**
- 400 Bad Request responses
- Validation error messages
- FluentValidation exceptions

**Common Validation Issues:**
```json
{
  "errors": [
    {
      "field": "CustomIdentifier",
      "message": "The CustomIdentifier length must be 10 characters."
    },
    {
      "field": "CustomIdentifier",
      "message": "CustomIdentifier must starts with HC"
    },
    {
      "field": "AppointmentDateTime",
      "message": "AppointmentDateTime must be greater than now."
    }
  ]
}
```

**Solutions:**
- Ensure CustomIdentifier is exactly 10 characters and starts with "HC"
- Verify appointment date is in the future
- Check all required fields are provided
- Validate date formats (ISO 8601)

#### 4. Business Rule Violations

**Symptoms:**
- 409 Conflict responses
- Business exception messages

**Common Business Rules:**
```
"There is also an active upcoming appointment for the patient"
→ Patient already has an active appointment, use reschedule instead

"There is already a record for this appointment identifier"
→ Appointment ID must be unique

"Health centre does not exist on the lookup table"
→ Invalid appointment location code
```

#### 5. Performance Issues

**Symptoms:**
- Slow response times
- Timeout errors
- High CPU/memory usage

**Diagnostic Steps:**
```bash
# Check Application Insights for slow requests
# Monitor Dynamics API call latency
# Review database connection pool usage
# Check for memory leaks

# Enable detailed logging temporarily
"Serilog": {
  "MinimumLevel": {
    "Default": "Debug"
  }
}
```

### Debugging Tools

#### 1. Local Debugging
```bash
# Run with detailed logging
dotnet run --environment Development

# Enable Swagger UI
"Swagger": {
  "UseSwagger": true,
  "UseSwaggerUI": true
}
```

#### 2. Health Check Endpoints
```bash
# Check overall health
GET /health

# Check specific dependencies
GET /health/ready
GET /health/live
```

#### 3. Application Insights Queries
```kusto
// Find errors in last 24 hours
exceptions
| where timestamp > ago(24h)
| summarize count() by type, outerMessage

// Slow requests
requests
| where timestamp > ago(1h)
| where duration > 5000
| project timestamp, name, duration, resultCode

// Dependency failures
dependencies
| where timestamp > ago(1h)
| where success == false
| project timestamp, name, type, resultCode, duration
```

### Support Contacts

#### Development Team
- **Primary Contact**: Development Team Lead
- **Email**: <EMAIL>
- **Response Time**: 4 hours during business hours

#### Infrastructure Team
- **Primary Contact**: Infrastructure Team Lead
- **Email**: <EMAIL>
- **Response Time**: 2 hours for critical issues

#### Business Team
- **Primary Contact**: Cancer Screening Program Manager
- **Email**: <EMAIL>
- **Response Time**: 8 hours during business hours

### Escalation Procedures

#### Severity Levels

**Critical (P1)**
- API completely unavailable
- Data corruption or loss
- Security breach
- **Response Time**: 1 hour
- **Resolution Time**: 4 hours

**High (P2)**
- Significant functionality impaired
- Performance degradation
- **Response Time**: 4 hours
- **Resolution Time**: 24 hours

**Medium (P3)**
- Minor functionality issues
- Non-critical bugs
- **Response Time**: 8 hours
- **Resolution Time**: 72 hours

**Low (P4)**
- Enhancement requests
- Documentation updates
- **Response Time**: 24 hours
- **Resolution Time**: Next release cycle

---

## Appendices

### A. API Request/Response Examples

See the [US3655 Implementation Notes](US3655-IMPLEMENTATION-NOTES.md) for detailed API examples with the new procedure fields.

### B. Database Schema

The system uses Dynamics 365 entities with the following key fields:
- `phcc_message`: Main message entity
- `phcc_procedurename`: Procedure name (new field)
- `phcc_procedurecode`: Procedure code (new field)
- `contact`: Patient entity
- `msemr_location`: Location lookup entity

### C. Change Log

#### Version 1.1 (US3655)
- Added optional `ProcedureName` field (max 200 characters)
- Added optional `ProcedureCode` field (max 50 characters)
- Updated validation rules for new fields
- Maintained backward compatibility

#### Version 1.0
- Initial release with core appointment management functionality
- SMS notification system
- Multi-language support
- Dynamics 365 integration

---

*This documentation is maintained by the PHCC Development Team. Last updated: 2025-01-17*
```

