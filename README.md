# PHCC Cancer Screening SMS API

[![Cancer Screening CI/CD Pipeline](https://github.com/public-health-care-center-CORP/CancerScreeningSMS/actions/workflows/app-CancerScreeningSMS-ci.yml/badge.svg?branch=main)](https://github.com/public-health-care-center-CORP/CancerScreeningSMS/actions/workflows/app-CancerScreeningSMS-ci.yml)

## Overview

The Cancer Screening SMS API is a specialized service designed to process SMS requests from upstream systems (primarily Fuji Synapse) for cancer screening appointments. The system manages appointment notifications, reminders, cancellations, and result communications through SMS messaging.

## Key Features

- **Appointment Management**: Create, reschedule, and cancel cancer screening appointments
- **SMS Notifications**: Automated SMS messaging for appointment confirmations and reminders
- **Multi-language Support**: English and Arabic language support based on patient nationality
- **Integration**: Seamless integration with Dynamics 365 Dataverse and Power Platform
- **Result Management**: Handle negative results and no-show notifications
- **Procedure Details**: Support for optional procedure names and codes (US3655 enhancement)

## Technology Stack

- **Language**: C# 10
- **Framework**: .NET 6.0 Web API
- **Architecture**: Clean Architecture with CQRS pattern
- **Database**: Dynamics 365 Dataverse
- **Logging**: Serilog with Application Insights
- **Documentation**: Swagger/OpenAPI

## Dependencies

- **Dynamics 365 Dataverse**: Primary data storage and patient management
- **Power Automate**: Workflow automation (monitoring scope)
- **Ooredoo SMS API**: SMS delivery service (monitoring scope)
- **Application Insights**: Telemetry and monitoring

## Quick Start

### Prerequisites
- .NET 6.0 SDK or later
- Visual Studio 2022 or JetBrains Rider
- Dynamics 365 environment access

### Setup
```bash
git clone https://github.com/public-health-care-center-CORP/CancerScreeningSMS.git
cd CancerScreeningSMS
dotnet restore
dotnet build
cd WebAPI
dotnet run
```

### API Access
- **Local URL**: `https://localhost:5001`
- **Swagger UI**: `https://localhost:5001/swagger`
- **Health Check**: `https://localhost:5001/health`

## API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/appointment/new` | Create new appointment |
| POST | `/api/appointment/reschedule` | Reschedule existing appointment |
| POST | `/api/appointment/cancel` | Cancel appointment |
| POST | `/api/appointment/negative-result` | Send negative result notification |
| POST | `/api/appointment/no-show` | Send no-show notification |

### Authentication
All endpoints require header-based authentication:
```http
X-Client-Id: [client-id]
X-Secret-Key: [secret-key]
```

## Documentation

📚 **[Complete Documentation](DOCUMENTATION.md)** - Comprehensive project documentation including:
- System architecture and design patterns
- Detailed API documentation with examples
- Business logic and domain models
- Configuration and deployment guides
- Testing and monitoring procedures
- Security and troubleshooting guides

🚀 **[Quick Reference Guide](QUICK-REFERENCE.md)** - Essential information for developers:
- Quick setup instructions
- API endpoint examples
- Common validation rules
- Testing commands
- Error troubleshooting

📋 **[US3655 Implementation Notes](US3655-IMPLEMENTATION-NOTES.md)** - Recent enhancement details:
- New procedure name and code fields
- API usage examples
- Validation rules
- Backward compatibility information

## Recent Updates

### Version 1.1 (US3655 Enhancement)
- ✅ Added optional `procedureName` field (max 200 characters)
- ✅ Added optional `procedureCode` field (max 50 characters)
- ✅ Updated validation rules for new fields
- ✅ Maintained full backward compatibility
- ✅ Enhanced API documentation and examples

## Project Structure

```
CancerScreeningSMS/
├── Core/                    # Shared infrastructure and utilities
├── Domain/                  # Business logic and domain models
├── WebAPI/                  # Presentation layer and API controllers
├── DOCUMENTATION.md         # Complete project documentation
├── QUICK-REFERENCE.md       # Developer quick reference
└── US3655-IMPLEMENTATION-NOTES.md  # Recent enhancement details
```

## Support

- **Development Team**: <EMAIL>
- **Infrastructure Team**: <EMAIL>
- **Business Team**: <EMAIL>

## Contributing

1. Create a feature branch from `main`
2. Make your changes following the coding standards
3. Add/update tests as needed
4. Update documentation if required
5. Submit a pull request for review

## License

This project is proprietary to the Public Health Care Center (PHCC) of Qatar.

---

*For detailed technical documentation, please refer to [DOCUMENTATION.md](DOCUMENTATION.md)*



